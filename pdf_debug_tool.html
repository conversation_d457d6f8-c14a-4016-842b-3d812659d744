<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF.js 诊断工具</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .test-item.pass { border-left-color: #28a745; }
        .test-item.fail { border-left-color: #dc3545; }
        .test-item.warn { border-left-color: #ffc107; }
        
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        
        .log-container {
            height: 300px;
            overflow-y: auto;
            background-color: #000;
            color: #00ff00;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PDF.js 诊断工具</h1>
        <p>此工具用于诊断 PDF.js 加载和渲染问题</p>

        <div class="section info">
            <h3>🔍 环境检查</h3>
            <div id="environment-checks"></div>
        </div>

        <div class="section">
            <h3>📁 资源文件检查</h3>
            <div id="resource-checks"></div>
            <button onclick="checkResources()">检查资源文件</button>
        </div>

        <div class="section">
            <h3>⚙️ PDF.js 配置检查</h3>
            <div id="config-checks"></div>
            <button onclick="checkPdfJsConfig()">检查配置</button>
        </div>

        <div class="section">
            <h3>📄 PDF 加载测试</h3>
            <div id="pdf-test-results"></div>
            <input type="file" id="pdf-file-input" accept=".pdf" style="margin: 10px 0;">
            <button onclick="testPdfLoading()">测试 PDF 加载</button>
        </div>

        <div class="section">
            <h3>📋 实时日志</h3>
            <div id="log-container" class="log-container"></div>
            <button onclick="clearLogs()">清空日志</button>
        </div>

        <div class="section">
            <h3>🛠️ 修复建议</h3>
            <div id="fix-suggestions"></div>
        </div>
    </div>

    <script>
        // 日志系统
        const logContainer = document.getElementById('log-container');
        const logs = [];

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            logs.push(logEntry);
            
            const div = document.createElement('div');
            div.style.color = type === 'error' ? '#ff6b6b' : 
                             type === 'warn' ? '#feca57' : 
                             type === 'success' ? '#48dbfb' : '#00ff00';
            div.textContent = logEntry;
            logContainer.appendChild(div);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLogs() {
            logContainer.innerHTML = '';
            logs.length = 0;
        }

        // 环境检查
        function checkEnvironment() {
            const checks = document.getElementById('environment-checks');
            const results = [];

            // 检查浏览器支持
            results.push({
                name: 'Web Workers 支持',
                pass: typeof Worker !== 'undefined',
                message: typeof Worker !== 'undefined' ? '支持' : '不支持 Web Workers'
            });

            results.push({
                name: 'ES6 模块支持',
                pass: typeof import !== 'undefined',
                message: typeof import !== 'undefined' ? '支持' : '不支持 ES6 模块'
            });

            results.push({
                name: 'Canvas 支持',
                pass: !!document.createElement('canvas').getContext,
                message: !!document.createElement('canvas').getContext ? '支持' : '不支持 Canvas'
            });

            results.push({
                name: 'ArrayBuffer 支持',
                pass: typeof ArrayBuffer !== 'undefined',
                message: typeof ArrayBuffer !== 'undefined' ? '支持' : '不支持 ArrayBuffer'
            });

            checks.innerHTML = results.map(result => 
                `<div class="test-item ${result.pass ? 'pass' : 'fail'}">
                    ${result.pass ? '✅' : '❌'} ${result.name}: ${result.message}
                </div>`
            ).join('');

            addLog('环境检查完成');
        }

        // 资源文件检查
        async function checkResources() {
            const checks = document.getElementById('resource-checks');
            const resources = [
                '/app/components/FreePdf/assets/build/pdf.mjs',
                '/app/components/FreePdf/assets/build/pdf.worker.mjs',
                '/app/components/FreePdf/assets/web/viewer.mjs',
                '/app/components/FreePdf/assets/web/viewer.css'
            ];

            const results = [];
            
            for (const resource of resources) {
                try {
                    const response = await fetch(resource, { method: 'HEAD' });
                    results.push({
                        name: resource,
                        pass: response.ok,
                        message: response.ok ? `可访问 (${response.status})` : `无法访问 (${response.status})`
                    });
                } catch (error) {
                    results.push({
                        name: resource,
                        pass: false,
                        message: `网络错误: ${error.message}`
                    });
                }
            }

            checks.innerHTML = results.map(result => 
                `<div class="test-item ${result.pass ? 'pass' : 'fail'}">
                    ${result.pass ? '✅' : '❌'} ${result.name}: ${result.message}
                </div>`
            ).join('');

            addLog('资源文件检查完成');
        }

        // PDF.js 配置检查
        async function checkPdfJsConfig() {
            const checks = document.getElementById('config-checks');
            const results = [];

            try {
                // 尝试导入 PDF.js
                addLog('正在导入 PDF.js...');
                const pdfjs = await import('/app/components/FreePdf/assets/build/pdf.mjs');
                
                results.push({
                    name: 'PDF.js 模块导入',
                    pass: true,
                    message: '成功导入'
                });

                // 检查 GlobalWorkerOptions
                if (pdfjs.GlobalWorkerOptions) {
                    results.push({
                        name: 'GlobalWorkerOptions',
                        pass: true,
                        message: '可用'
                    });

                    // 检查 worker 配置
                    const workerSrc = pdfjs.GlobalWorkerOptions.workerSrc;
                    results.push({
                        name: 'Worker 源路径',
                        pass: !!workerSrc,
                        message: workerSrc || '未设置'
                    });
                } else {
                    results.push({
                        name: 'GlobalWorkerOptions',
                        pass: false,
                        message: '不可用'
                    });
                }

                // 检查版本信息
                if (pdfjs.version) {
                    results.push({
                        name: 'PDF.js 版本',
                        pass: true,
                        message: pdfjs.version
                    });
                }

            } catch (error) {
                results.push({
                    name: 'PDF.js 模块导入',
                    pass: false,
                    message: `导入失败: ${error.message}`
                });
                addLog(`PDF.js 导入失败: ${error.message}`, 'error');
            }

            checks.innerHTML = results.map(result => 
                `<div class="test-item ${result.pass ? 'pass' : 'fail'}">
                    ${result.pass ? '✅' : '❌'} ${result.name}: ${result.message}
                </div>`
            ).join('');

            addLog('PDF.js 配置检查完成');
        }

        // PDF 加载测试
        async function testPdfLoading() {
            const fileInput = document.getElementById('pdf-file-input');
            const results = document.getElementById('pdf-test-results');
            
            if (!fileInput.files.length) {
                addLog('请先选择一个 PDF 文件', 'warn');
                return;
            }

            const file = fileInput.files[0];
            addLog(`开始测试 PDF 文件: ${file.name}`);

            try {
                // 导入 PDF.js
                const pdfjs = await import('/app/components/FreePdf/assets/build/pdf.mjs');
                
                // 设置 worker
                pdfjs.GlobalWorkerOptions.workerSrc = '/app/components/FreePdf/assets/build/pdf.worker.mjs';
                
                // 读取文件
                const arrayBuffer = await file.arrayBuffer();
                addLog('文件读取成功');

                // 加载 PDF 文档
                const loadingTask = pdfjs.getDocument(arrayBuffer);
                const pdf = await loadingTask.promise;
                
                addLog(`PDF 加载成功，共 ${pdf.numPages} 页`);

                // 测试渲染第一页
                const page = await pdf.getPage(1);
                addLog('第一页获取成功');

                const viewport = page.getViewport({ scale: 1.0 });
                addLog(`页面尺寸: ${viewport.width} x ${viewport.height}`);

                // 创建 canvas 测试渲染
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                canvas.height = viewport.height;
                canvas.width = viewport.width;

                const renderContext = {
                    canvasContext: context,
                    viewport: viewport
                };

                await page.render(renderContext).promise;
                addLog('页面渲染成功', 'success');

                results.innerHTML = `
                    <div class="test-item pass">
                        ✅ PDF 加载和渲染测试通过
                        <br>文件: ${file.name}
                        <br>页数: ${pdf.numPages}
                        <br>第一页尺寸: ${viewport.width} x ${viewport.height}
                    </div>
                `;

            } catch (error) {
                addLog(`PDF 测试失败: ${error.message}`, 'error');
                results.innerHTML = `
                    <div class="test-item fail">
                        ❌ PDF 加载测试失败: ${error.message}
                    </div>
                `;
            }
        }

        // 生成修复建议
        function generateFixSuggestions() {
            const suggestions = document.getElementById('fix-suggestions');
            suggestions.innerHTML = `
                <h4>常见问题及解决方案：</h4>
                <div class="test-item">
                    <strong>1. Worker 路径问题</strong><br>
                    确保 pdf.worker.mjs 文件路径正确，通常应该设置为相对于当前页面的路径
                </div>
                <div class="test-item">
                    <strong>2. CORS 问题</strong><br>
                    确保 PDF.js 资源文件可以被正确访问，检查服务器 CORS 配置
                </div>
                <div class="test-item">
                    <strong>3. 模块导入问题</strong><br>
                    确保使用正确的 ES6 模块导入语法，并且服务器支持 ES6 模块
                </div>
                <div class="test-item">
                    <strong>4. 容器可见性问题</strong><br>
                    确保 PDF 容器在 DOM 中可见且有正确的尺寸
                </div>
            `;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('PDF.js 诊断工具已启动');
            checkEnvironment();
            generateFixSuggestions();
        });

        // 捕获全局错误
        window.addEventListener('error', function(event) {
            addLog(`全局错误: ${event.error?.message || event.message}`, 'error');
        });

        window.addEventListener('unhandledrejection', function(event) {
            addLog(`未处理的 Promise 拒绝: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
