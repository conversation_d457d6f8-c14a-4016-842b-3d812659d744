# PDF.js 内容加载渲染问题诊断与修复

## 问题描述

PDF 内容没有被正确加载渲染，浏览器也没有提示错误，表现为：
- PDF 容器显示空白
- 没有明显的错误信息
- 页面看起来正常但内容不显示

## 问题诊断

### 1. 可能的原因分析

1. **Worker 路径配置错误**
   - PDF.js 依赖 Web Worker 进行 PDF 解析
   - Worker 文件路径不正确会导致静默失败

2. **资源文件加载失败**
   - PDF.js 核心库或 Worker 文件无法访问
   - CORS 或路径问题导致资源加载失败

3. **初始化时序问题**
   - DOM 元素未完全准备就开始初始化
   - 容器可见性检查不充分

4. **配置参数缺失**
   - GlobalWorkerOptions 未正确设置
   - 必要的配置参数缺失

## 修复方案

### 1. 修复 Worker 路径配置

**问题：** viewer.mjs 中使用相对路径可能导致 Worker 加载失败

**修复前：**
```javascript
workerSrc: {
  value: '../build/pdf.worker.mjs',
  kind: OptionKind.WORKER
}
```

**修复后：**
```javascript
workerSrc: {
  value: '/app/components/FreePdf/assets/build/pdf.worker.mjs',
  kind: OptionKind.WORKER
}
```

### 2. 增强初始化检查机制

**修复前：** 简单的容器存在检查
**修复后：** 全面的 DOM 元素和资源检查

```javascript
const initPdfViewer = async () => {
  try {
    console.log('开始初始化 PDF.js...')
    
    // 检查必要的 DOM 元素
    const requiredElements = ['outerContainer', 'viewerContainer', 'viewer']
    for (const elementId of requiredElements) {
      const element = document.getElementById(elementId)
      if (!element) {
        throw new Error(`必需的 DOM 元素 ${elementId} 不存在`)
      }
      console.log(`✓ DOM 元素 ${elementId} 已找到`)
    }
    
    // 导入并配置 PDF.js
    const pdfjs = await import('./assets/build/pdf.mjs')
    console.log('✓ PDF.js 核心库导入成功，版本:', pdfjs.version)
    
    // 设置 Worker 路径
    const workerPath = '/app/components/FreePdf/assets/build/pdf.worker.mjs'
    pdfjs.GlobalWorkerOptions.workerSrc = workerPath
    console.log('✓ PDF.js worker 路径设置为:', workerPath)
    
    // 测试 Worker 文件可访问性
    try {
      const response = await fetch(workerPath, { method: 'HEAD' })
      if (!response.ok) {
        throw new Error(`Worker 文件无法访问: ${response.status}`)
      }
      console.log('✓ Worker 文件可访问')
    } catch (workerError) {
      console.warn('Worker 文件检查失败:', workerError)
    }
    
    // 导入查看器
    const viewer = await import('./assets/web/viewer.mjs')
    pdfViewer.value = viewer
    console.log('✓ PDF.js 查看器导入成功')
    
    console.log('✅ PDF.js 初始化完成')
  } catch (error) {
    console.error('❌ PDF.js 初始化失败:', error)
    throw error
  }
}
```

### 3. 增强容器可见性检查

**修复前：** 基本的尺寸和可见性检查
**修复后：** 详细的容器状态日志和检查

```javascript
console.log(`PDF容器检查 (尝试 ${retryCount + 1}):`, {
  width: rect.width,
  height: rect.height,
  offsetParent: !!container.offsetParent,
  display: computedStyle.display,
  visibility: computedStyle.visibility,
  opacity: computedStyle.opacity,
  isVisible
})
```

### 4. 添加详细的事件监听和日志

```javascript
// 监听文档初始化事件
viewer.PDFViewerApplication.eventBus._on('documentinit', (event) => {
  console.log('PDF 文档初始化:', event.source)
})

// 监听页面渲染事件
viewer.PDFViewerApplication.eventBus._on('pagerendered', (event) => {
  console.log(`PDF 页面 ${event.pageNumber} 渲染完成`)
})

// 监听页面渲染开始事件
viewer.PDFViewerApplication.eventBus._on('pagerender', (event) => {
  console.log(`PDF 页面 ${event.pageNumber} 开始渲染`)
})
```

## 诊断工具

### 1. PDF.js 诊断工具 (pdf_debug_tool.html)
- 环境兼容性检查
- 资源文件可访问性测试
- PDF.js 配置验证
- PDF 加载测试

### 2. 修复效果测试页面 (pdf_test_page.html)
- 实时日志监控
- PDF 文件测试
- 修复验证
- 错误捕获

## 常见问题排查

### 1. Worker 加载失败
**症状：** PDF 不显示，控制台可能有 Worker 相关错误
**解决：** 检查 Worker 文件路径，确保文件可访问

### 2. CORS 问题
**症状：** 跨域错误，资源加载失败
**解决：** 配置服务器 CORS 头，或使用相同域名的资源

### 3. 容器不可见
**症状：** PDF.js 初始化失败，容器检查不通过
**解决：** 确保容器有正确的尺寸和可见性

### 4. 模块导入失败
**症状：** ES6 模块导入错误
**解决：** 检查浏览器兼容性，确保服务器支持 ES6 模块

## 验证步骤

1. **打开浏览器开发者工具**
   - 查看 Console 标签页的日志输出
   - 检查是否有错误信息

2. **检查网络请求**
   - 查看 Network 标签页
   - 确认 PDF.js 相关文件是否成功加载

3. **测试 PDF 文件**
   - 选择一个小的 PDF 文件进行测试
   - 观察加载过程和渲染结果

4. **使用诊断工具**
   - 运行 pdf_debug_tool.html 进行全面检查
   - 使用 pdf_test_page.html 进行实际测试

## 预期效果

修复后应该看到：
- ✅ 详细的初始化日志输出
- ✅ PDF 内容正确显示
- ✅ 页面渲染事件正常触发
- ✅ 没有 Worker 加载错误
- ✅ 容器状态检查通过

## 注意事项

1. **浏览器兼容性**
   - 确保浏览器支持 Web Workers 和 ES6 模块
   - 某些旧版本浏览器可能不支持

2. **文件大小限制**
   - 大型 PDF 文件可能需要更长的加载时间
   - 考虑添加加载进度指示器

3. **内存使用**
   - PDF.js 会占用较多内存
   - 大文件或多页面可能影响性能

4. **服务器配置**
   - 确保服务器正确配置 MIME 类型
   - 检查 CORS 和缓存策略
