// 繁体中文
export default {
  common: {
    site_name: '精挑翻譯',
    title: '精挑翻譯 - AI擇優翻譯、雙語對照網頁翻譯',
    description:
      '精挑翻譯(SelectTranslate)是一款可以免費使用的瀏覽器翻譯擴充, 支援AI擇優翻譯、雙語對照網頁翻譯、輸入翻譯、文字翻譯、滑鼠懸停翻譯、劃詞翻譯、PDF文檔翻譯等功能。支援微軟翻譯、Google翻譯、DeepSeek、DeepL、OpenAI（ChatGPT）、Gemini、<PERSON>、通義千問等 AI 翻譯模型。一款能提升工作與學習效率的翻譯工具，為您提供好用、準確、符合目標語境的翻譯體驗。',
    page_does_not_exist: '頁面不存在',
    page_does_not_exist_tip: '抱歉！您要訪問的頁面不存在',
    back_to_home_page: '返回首頁',
    confirm: '確認',
    cancel: '取消',
    ai_selected: 'AI 擇優',
    translation_results: '翻譯結果',
    engins: {
      google: 'Google 翻譯',
      microsoft: '微軟翻譯',
      transmart: '騰訊翻譯',
      deepl: 'DeepL',
      openai: 'OpenAI',
      gemini: 'Gemini',
      claude: '<PERSON>',
      zhipu: '智譜GLM',
      qwen: '通義千問', // 通义千问
      lingyiwanwu: '零一萬物',
      deepseek: 'DeepSeek',
      doubao: '豆包',
      grok: 'Grok',
      yandex: 'Yandex'
    },
    close: '關閉',
    model_unavailable: '模型不可用，請重新選擇',
    error_tip: '提示',
    swipe_to_see_more: '左右滑動查看更多',
    loading: '載入中...',
    retry: '重試'
  },
  theme: {
    light: '淺色',
    dark: '深色',
    system: '跟隨系統'
  },
  header: {
    // 页头
    home: '首頁', // 首页
    pricing: '價格', // 价格
    docs: '教程', // 教程
    selected_translation: 'AI 擇優翻譯', // AI 择优翻译
    file_translation: '文件翻譯', // 文件翻译
    account: '賬戶', // 账户
    billing: '賬單', // 账单
    pdf_plus: 'PDF Plus', // PDF Plus
    feedback: '問題反饋', // 问题反馈
    signup: '註冊', // 注册(报名)
    login: '登入', // 登录
    logout: '退出' // 退出
  },
  auth: {
    // 用户授权认证模块：注册、激活、登录、重置密码
    common: {
      home: '首頁', // 首页
      email_label: '郵箱地址', // 邮箱地址
      password_label: '密碼', // 密码
      verify_code_label: '驗證碼', // 验证码
      verify_code_send_label: '獲取驗證碼', // 获取验证码
      verify_code_has_been_send: '驗證碼已發送', // 验证码已发送
      verify_code_countdown_retry: '{seconds}秒後重試', // {seconds}秒后重试
      resend: '重新發送', // 重新发送
      no_account_yet: '還沒有賬號？', // 还没有账号？
      have_an_account: '已經有賬號？', // 已经有账号？
      or_login_using: '或', // 或
      login_with_wechat: '使用微信賬號登入', // 使用微信账号登录
      login_with_google: '使用 Google 賬號登入', // 使用 Google 账号登录
      welcome_to_login: '歡迎登入', // 欢迎登录
      confirm_agreement: '註冊即表示您同意', // 注册即表示您同意
      terms_of_service: '服務條款', // 服务条款
      and: '和', // 和
      privacy_policy: '隱私政策', // 隐私政策
      messages: {
        email_required: '請輸入郵箱地址', // 请输入邮箱地址
        email_format_incorrect: '請輸入正確的郵箱地址', // 请输入正确的邮箱地址
        email_confirm_success: '郵箱地址驗證成功，請登入', // 邮箱地址验证成功，请登录
        password_required: '請輸入密碼', // 请输入密码
        password_format_incorrect_1: '密碼必須是字母、數字或符號組成', // 密码必须是字母、数字或符号组成
        password_format_incorrect_2: '密碼長度為6到20個字符', // 密码长度为6到20个字符
        verify_code_required: '請輸入郵件驗證碼', // 请输入邮件验证码
        verify_code_email_send_tip_1: '已向您的郵箱地址【', // 已向您的邮箱地址【
        verify_code_email_send_tip_2: '】發送了驗證碼，請查收郵件！', // 】发送了验证码，请查收邮件！
        new_password_required: '請設置新密碼', // 请设置新密码
        reset_password_success: '重設密碼成功' // 重置密码成功
      }
    },
    signup: {
      title: '註冊帳號'
    },
    signupconfirm: {
      title: '驗證郵箱地址' // 验证邮箱地址
    },
    login: {
      title: '登入'
    },
    reset_password: {
      title: '重設密碼', // 重设密码
      new_password_label: '新密碼', // 新密码
      new_password_placeholder: '設置新密碼' // 设置新密码
    }
  },
  account: {
    account: '帳戶', // 账户
    plan: '套餐', // 套餐
    email_address: '郵箱地址', // 邮箱地址
    wechat_nickname: '微信昵稱', // 微信昵称
    google_nickname: 'Google 昵稱', // Google 昵称
    current_plan: '當前套餐', // 当前套餐
    free_version: '免費版', // 免费版
    trial_version: '試用版', // 试用版
    free_trial_plus: '免費試用 Plus 會員', // 免费试用 Plus 会员
    apply_for_free_trial_plus_tip_1: '您可免費申請試用 Plus 會員【 ',
    apply_for_free_trial_plus_tip_2: ' 】天！',
    apply_for_free_trial_plus_tip_3: '確認要申請嗎？',
    upgrade_to_plus: '升級為 Plus 會員', // 升级为 Plus 会员
    upgrade_to_official_version_plus: '升級為正式版 Plus 會員', // 升级为正式版 Plus 会员
    subscription_billing_time_tip1: '您訂閱的 Plus 會員將在試用期結束後於', // 订阅扣费 提示1
    subscription_billing_time_tip2: '自動扣費生效', // 订阅扣费 提示2
    renewal_or_subscribe: '續費 / 訂閱',
    monthly_subscription: '月度訂閱會員',
    yearly_subscription: '年度訂閱會員', // 年度订阅会员
    next_renewal_time: '下次續費時間', // 下次续费时间
    unsubscribe: '取消訂閱',
    unsubscribe_dialog_title: '取消您的訂閱',
    unsubscribe_dialog_content_1: '您目前訂閱的套餐服務將於',
    unsubscribe_dialog_content_2: '到期，取消訂閱後您仍可在服務期結束前繼續使用。',
    unsubscribe_dialog_content_3: '如果您確認要取消訂閱，請點選【取消訂閱】按鈕。',
    unsubscribe_dialog_content_4: '如果您想繼續保留目前訂閱，請點選【返回】按鈕。',
    unsubscribe_dialog_btn_back: '返回', // 返回
    unsubscribe_success_info: '您已成功取消訂閱', // 您已成功取消订阅
    unsubscribe_failed_info: '取消訂閱失敗，請稍後再試', // 取消訂閱失敗，請稍後再試
    usage_of_trial_plan_quota: '試用餐額度', // 试用餐额度
    usage_of_this_month_plan_quota: '本月套餐額度', // 本月套餐额度
    remaining_translation_quota: '翻譯額度剩餘',
    pdf_plus_pages_remaining: 'PDF Plus 頁數剩餘', // PDF Plus 页数剩余
    pages: '頁', // 页
    quota_reset_time: '下次額度重置時間', // 下次额度重置时间
    plan_will_expire_on_1: '套餐服務將於', // 套餐服务将于
    plan_will_expire_on_2: '到期', // 到期
    plan_expired_on_1: '套餐服務已於', // 套餐服务已于
    plan_expired_on_2: '到期', // 到期
    usage_of_add_on_quota: '加量包額度', // 加量包额度
    purchase_translation_quota_add_on_package: '購買翻譯額度加量包', // 购买翻译额度加量包
    purchase_pdf_plus_add_on_package: '購買 PDF Plus 加量包', // 购买 PDF Plus 加量包
    purchase_add_on_package_tips: '加量包額度僅限 Plus 會員有效期內購買和使用', // 加量包额度仅限 Plus 会员有效期内购买和使用
    not_logged_in: '未登入', // 未登录
    not_logged_in_tip: '請先登入', // 请先登录
    login: '登入', // 登录
    subscription_renewal_failed: '訂閱續費失敗', // 订阅续费失败
    subscription_renewal_failed_tip_1: '您的 Plus 會員續費未成功，因您的支付卡餘額不足。請在充值後重新嘗試訂閱，如有疑問，請透過電子郵件 {email} 聯繫我們獲取支援。', // 您的 Plus 会员续费未成功，因您的支付卡余额不足。请在充值后重新尝试订阅，如有疑问，请通过电子邮件 <EMAIL> 联系我们获取支持。
    subscription_renewal_failed_tip_2: '您的 Plus 會員續費未成功，因您的支付卡已過期。請在更換支付卡後重新嘗試訂閱，如有疑問，請通過電子郵件 {email} 聯繫我們獲取支持。', // 您的 Plus 会员续费未成功，因您的支付卡已过期。请在更换支付卡后重新尝试订阅，如有疑问，请通过电子邮件 <EMAIL> 联系我们获取支持。
    subscription_renewal_failed_tip_3: '您的 Plus 會員訂閱續費扣款失敗，原因可能是銀行攔截，請在更換支付卡後重新嘗試訂閱。如有疑問，請透過電子郵件 {email} 聯繫我們獲取支援。' // 您的 Plus 会员订阅续费失败。如有疑问，请通过电子邮件 <EMAIL> 联系我们获取支持。
  },
  billing: {
    title: '賬單', // 账单
    service_order: '服務訂單', // 服务订单
    order_columns: {
      // 订单列表表头
      index: '序號',
      pay_time: '付款時間',
      pay_order_no: '訂單編號',
      product_name: '產品名稱',
      buy_mode: '服務週期', // 服务周期
      amount: '金額',
      pay_way: '支付方式',
      service_begin_time: '服務開始時間',
      service_end_time: '服務結束時間'
    },
    product_names: {
      // 产品名称
      plus_name: 'Plus 會員',
      general_token_name: '翻譯額度加量包',
      pdf_plus_name: 'PDF Plus 加量包', //
      pdf_plus: 'PDF Plus',
      translation_quota: '翻譯額度', // 翻译额度
      quantity: '數量', // 数量
      tatal: '總計', // 总计
      pages: '頁' // 页
    },
    buy_modes: {
      // 购买模式
      try: '試用',
      weekly: '按週',
      monthly: '按月',
      quarterly: '按季',
      yearly: '按年',
      perpetual: '永久' // 永久
    },
    pay_ways: {
      // 支付方式
      platform_gift: '平台贈送',
      wechat: '微信支付',
      alipay: '支付寶',
      stripe: 'Stripe',
      coupon_code: '兌換碼'
    },
    refund_order: {
      // 退款订单
      refund_success: '已退款',
      refund_amount: '退款金額',
      refund_time: '退款時間'
    }
  },
  home: {
    // 首页
    hero: {
      title: '更智慧的 AI 翻譯擴充！',
      description_1: '支援雙語對照網頁翻譯、輸入翻譯、劃詞翻譯、PDF 翻譯、滑鼠懸停翻譯等常用的翻譯功能。',
      description_2: '業界首創的 AI 擇優翻譯功能：多模型翻譯 + AI 評分擇優，為您精準呈現最符合情境的專業級翻譯結果。',
      extension_pc_chrome_label: 'Chrome 擴充', // Chrome 扩展
      extension_pc_edge_label: 'Edge 擴充', // Edge 扩展
      extension_pc_safari_label: 'Safari 擴充', // Safari 扩展
      extension_pc_firefox_label: 'Firefox 擴充', // Firefox 扩展
      extension_pc_360browser_label: '360瀏覽器擴充', // 360浏览器扩展
      extension_pc_crx_label: 'crx 安裝包', // crx 安装包
      extension_pc_zip_label: 'zip 安裝包' // zip 安装包
    },
    scenarios: {
      // 应用场景
      social: {
        title: '社交互動', // 社交互动
        description: '無障礙閱讀，跨語言交流'
      },
      information: {
        title: '資訊瀏覽',
        description: '國際資訊，即時掌握'
      },
      study: {
        title: '學習研究',
        description: '全球知識，輕鬆獲取'
      },
      document: {
        title: '文檔翻譯',
        description: '學術、辦公，便捷高效'
      },
      international_trade: {
        title: '國際貿易',
        description: '自由溝通，服務全球'
      }
    },
    engins: {
      title: '匯聚全球頂級 AI 翻譯模型'
    },
    mission: {
      // 使命
      title: '推動資訊平權，實現母語自由',
      description1: '「精挑翻譯」名稱來自於我們首創的「AI擇優翻譯」功能。以智慧和應用創新為基礎，我們將持續打造更好用翻譯產品。',
      description2: '我們的使命是讓每個人都能無障礙地閱讀，用母語就能跨語言辦公和交流。推動資訊平權，實現母語自由！'
    },
    bilingual_translation: {
      title: '雙語對照網頁翻譯',
      description: '智慧辨識並翻譯網頁內容，在原文後顯示譯文，實現雙語對照閱讀。支援各種語言的網頁翻譯，幫助您無障礙暢遊全球知識海洋！',
      feature1: '點選擴充插件懸浮按鈕，一鍵開啟雙語閱讀。',
      feature2: '提供多種預設譯文樣式，同時支援自訂譯文樣式。',
      feature3: '自由選擇 AI 翻譯模型，支持 100 多種語言互譯。'
    },
    ai_selected_translation: {
      title: 'AI 擇優翻譯',
      description: '使用多個翻譯模型對同一原文進行翻譯會得到多個不同的結果，透過智慧評分從中擇優獲取最佳譯文，能大幅提升翻譯結果的準確度和可靠性。',
      feature1: '可設定多個翻譯模型同時進行翻譯。',
      feature2: '使用較強 AI 模型對多個翻譯結果進行評分擇優。',
      feature3: '每個模型的翻譯結果都有直覺的評分及分析說明，品質差異對比一目了然。'
    },
    input_translation: {
      title: '輸入翻譯',
      description: '在網頁輸入框中輸入內容即可一鍵翻譯。讓您用母語就能跨語言輸入、回覆和交流，輕鬆實現母語自由。 ',
      feature1: '深入應用場景，擺脫使用傳統翻譯工具的複製、切換、貼上等繁瑣流程。 ',
      feature2: '基於擇優翻譯，即時取得最佳譯文結果，給您輸入法般的使用體驗。',
      feature3: '翻譯記錄管理，方便您隨時查看和重複使用翻譯記錄。 '
    },
    pdf_free_translation: {
      title: 'PDF 翻譯',
      description: '完全免費的PDF 翻譯功能，日常外語文件快速翻譯，滿足您大部分 PDF 文件的翻譯需求。',
      feature1: '保留原文排版：每段譯文的位置與原文保持一致。',
      feature2: '逐頁雙語對照：原文檔頁和譯頁左右逐頁對應，輕鬆比較閱讀。',
      feature3: '翻譯結果匯出：可自訂譯文的顯示樣式和匯出文件格式。'
    },
    pdf_plus_translation: {
      title: 'PDF Plus',
      description: 'PDF Plus 為學術論文和各類專業文件的翻譯而設計。基於領先的AI 視覺處理技術，解決了複雜文件中因為各類公式、圖表等識別不準確而導致翻譯結果和排版錯亂的難題。',
      feature1: '精確解析各類公式、複雜圖表、程式碼片段等專業內容。',
      feature2: '支援掃描版PDF 的解析翻譯。',
      feature3: '雙欄、三欄佈局識別轉換。'
    },
    epub_translation: {
      title: 'ePub 電子書翻譯',
      description: 'ePub 電子書翻譯功能，可將各種外語 ePub 電子書翻譯為雙語形式或純目標語言形式進行閱讀，完美相容於 Kindle 等各種電子書閱讀器。'
    },
    mouse_hover_translation: {
      title: '滑鼠懸停翻譯',
      description: '支援【段落翻譯】和【區域翻譯】兩種模式，滑鼠懸停 + 快捷鍵，可快速翻譯網頁中單一段落文字或區域中的多個段落文字。',
      feature1: '段落翻譯：只翻譯滑鼠懸停段落的單段文字。',
      feature2: '區域翻譯：翻譯滑鼠懸停所選網頁區域下的所有段落，讓懸停翻譯更靈活。',
      feature3: '可自訂懸停翻譯的快捷鍵，滿足您的個人需求。'
    },
    highlight_translation: {
      title: '劃詞翻譯',
      description: '直接劃選單字或文字即可翻譯。給您隨手、隨選的輕量翻譯體驗。',
      feature1: '單字翻譯：詞性、音標、發音、例句，全方位學習。',
      feature2: '結合單字的上下文語境，提供詳細的翻譯結果分析。',
      feature3: '翻譯模型可按需切換。'
    },
    text_translation: {
      title: '文本翻譯',
      description: '聚合多個翻譯平台的引擎，供您随心切換。'
    },
    compare_translation: {
      title: '對比翻譯',
      description: '一鍵啟用多個翻譯模型，即時並列呈現各家譯文，差異一目了然，輕鬆選出最合心意的版本。'
    }
  },
  pricing: {
    // 价格
    hero: {
      title: '邁入 AI 翻譯新時代',
      description1: '更智慧、更精準、更好用',
      description2: '升級 Plus 會員，尊享',
      description3: '頂級 AI 翻譯服務'
    },
    dialog: {
      // 用户未登录弹窗提示
      title: '未登入',
      message: '請先登入',
      button: '登 入'
    },
    cycle_name: {
      weekly: '週會員', // 周会员
      monthly: '每月會員', // 月度会员
      quarterly: '季會員', // 季度会员
      yearly: '年度會員' // 年度会员
    },
    cycle_unit: {
      weekly: '/ 週',
      monthly: '/ 月',
      quarterly: '/ 季',
      yearly: '/ 年'
    },
    plans: {
      current_plan: '當前套餐', // 当前套餐
      free: {
        title: '免費版',
        description: '無需付費',
        button: {
          login_or_signup: '登入 / 註冊' // 登录 / 注册
        },
        options: {
          title: '', // 无
          description: '', // 无
          models_title: '翻譯模型',
          models: {
            model0: {
              title: '微軟翻譯',
              sub_title: '',
              tips: ''
            },
            model1: {
              title: '谷歌翻譯',
              sub_title: '',
              tips: ''
            },
            model2: {
              title: '智譜 GLM 翻譯',
              sub_title: 'GLM-4.5-Flash',
              tips: ''
            },
            model3: {
              title: '騰訊翻譯',
              sub_title: '',
              tips: ''
            },
            model4: {
              title: 'Yandex 翻譯',
              sub_title: '',
              tips: ''
            },
            model5: {
              title: '其他翻譯模型',
              sub_title: '可自訂 API Key',
              tips: ''
            }
          },
          features_title: '免費版的功能', // 免费版的功能
          features: {
            feature0: {
              title: '網頁翻譯',
              sub_title: '開放使用',
              tips: ''
            },
            feature1: {
              title: 'AI 擇優翻譯',
              sub_title: '開放使用',
              tips: ''
            },
            feature2: {
              title: '輸入翻譯',
              sub_title: '開放使用',
              tips: ''
            },
            feature3: {
              title: '劃詞翻譯',
              sub_title: '開放使用',
              tips: ''
            },
            feature4: {
              title: 'PDF 翻譯',
              sub_title: '開放使用',
              tips: ''
            },
            feature5: {
              title: 'ePub 電子書翻譯',
              sub_title: '開放使用',
              tips: ''
            },
            feature6: {
              title: 'MinerU 文件翻譯',
              sub_title: '自訂 API Key',
              tips: '可至 MinerU 官網申請 API Token，每個帳號每天享有 2000 頁最高優先權解析額度。'
            }
          }
        }
      },
      plus: {
        title: 'Plus 會員', // Plus 会员
        description: '尊享頂級 AI 翻譯服務新體驗！',
        button: {
          free_trial: '免費試用 3 天',
          purchase: '購買',
          renewal: '續費',
          subscribe: '訂閱' // 订阅
        },
        options: {
          title: '包含免費版所有功能',
          description: '', // 无
          models_title: 'Plus 尊享頂級 AI 翻譯模型',
          models_description: 'Plus 會員可以直接使用 DeepSeek、OpenAI、Gemini、智譜AI、豆包、通義千問 高級翻譯模型。高級模型支援多版本自選，您可以根據場景選擇適合的版本。',
          models: {
            model0: {
              title: 'DeepSeek 翻譯',
              sub_title: '',
              tips: ''
            },
            model1: {
              title: 'OpenAI 翻譯',
              sub_title: '可選 GPT-5',
              tips: ''
            },
            model2: {
              title: 'Gemini 翻譯',
              sub_title: '',
              tips: ''
            },
            model3: {
              title: '豆包 翻譯',
              sub_title: '',
              tips: ''
            },
            model4: {
              title: '智譜GLM 翻譯',
              sub_title: '',
              tips: ''
            },
            model5: {
              title: '通義千問 翻譯',
              sub_title: '',
              tips: ''
            }
          },
          features_title: 'Plus 尊享高級功能',
          features: {
            feature0: {
              title: 'AI 擇優翻譯（高級版）',
              sub_title: '',
              tips: '使用 Plus 會員尊享的多個進階模型同時進行翻譯，並對翻譯結果進行評分擇優，可顯著提升翻譯結果的準確度與可靠性。'
            },
            feature1: {
              title: '輸入翻譯（高級版）',
              sub_title: '',
              tips: '使用 Plus 會員尊享的多個進階模型同時進行翻譯，並對翻譯結果進行評分擇優，可顯著提升翻譯結果的準確度與可靠性。'
            },
            feature2: {
              title: 'MinerU 複雜格式文件翻譯（高級版）',
              sub_title: '',
              tips: 'Plus會員可直接使用，每月 10000 頁最高優先級解析額度。支援 PDF、Word、PPT、圖片的翻譯，能實現各種複雜公式、表格、掃描版文件的辨識與翻譯。'
            },
            feature3: {
              title: 'PDF Plus',
              sub_title: '',
              tips: '每月 300 頁 AI高保真 PDF 文件翻譯，支援複雜公式、表格、掃描版文件的識別和翻譯。'
            },
            feature4: {
              title: '優先的售後服務支持',
              sub_title: '',
              tips: '服務支援郵箱：'
            }
          }
        }
      }
    }
  },
  buy: {
    // 购买页面（选择套餐、购买模式、支付方式）
    title: '購買 / 訂閱 Plus 會員', // 购买 / 订阅 Plus 会员
    plus_membership: 'Plus 會員', // Plus 会员
    purchase_mode: '購買模式', // 购买模式
    purchase: '購買', // 购买
    subscribe: '訂閱', // 订阅
    plans: '套餐', // 套餐
    plans_detail: '套餐明細',
    purchase_cycle: {
      // 周期
      week: '週',
      month: '月',
      quarter: '季',
      year: '年'
    },
    pay_way: '支付方式',
    pay_way_items: {
      // 支付方式
      wechat: '微信支付',
      alipay: '支付寶',
      stripe: 'Stripe',
      coupon_code: '兌換碼'
    },
    confirm_payment: '確認支付', // 确认支付
    confirm_subscribe: '確認訂閱', // 确认订阅
    remaining_payment_time: '剩餘支付時間', // 剩余支付时间
    payment_amount: '支付金額', // 支付金额
    wechat_scan_qrcode_to_pay: '請使用微信掃碼支付', // 请使用微信扫码支付
    alipay_scan_qrcode_to_pay: '請使用支付寶掃碼支付', // 请使用支付宝扫码支付
    please_pay_on_time: '請及時支付' // 请及时支付
  },
  resource: {
    title: '服務資源加量包',
    resource_ype: '資源類型',
    translation_quota_add_on_package: '翻譯額度（TQ）加量包',
    pdf_plus_add_on_package: 'PDF Plus 加量包',
    resource_specifications: '資源規格',
    add_on_package: '加量包',
    pages: '頁', // 页
    quantity_to_purchase: '購買數量', // 购买数量
    no_data: '暫無數據', // 暂无数据
    payment_methods: '支付方式', // 支付方式
    amount_to_pay: '支付金額', // 支付金额
    buy_now: '立即購買' // 立即购买
  },
  selected_tsl_desc: {
    title: '為什麼要用擇優翻譯？',
    description_1: '單一模型難以應付翻譯中多場景、多語種及複雜語境等多元需求。',
    description_2: '擇優翻譯：多模型協同翻譯 + AI 評分擇優，精準輸出最佳譯文。',
    description_3: '優點：有效排除錯誤結果，顯著提升翻譯質量，尤其適合高準確度需求的翻譯場景。',
    description_4: '請看以下擇優翻譯的比較效果',
    examples: {
      original_text: '原文',
      best_translation: '最佳譯文',
      selected_model: '擇優模型',
      table: {
        translation_model: '翻譯模型',
        translation_result: '譯文',
        translation_score: '評分',
        translation_rationale: '評分說明',
        translation_model_version: '模型版本'
      },
      note_01: '說明：上述範例採用同價位層級的不同模型進行翻譯，並對翻譯結果評分所得的資料。',
      note_02: 'AI 模型翻譯及評分有一定的隨機性，重現測驗時結果可能略有差異，但整體相近。',
      note_03: '以上數據僅供參考。強烈建議您直接體驗: '
    }
  },
  faq_common: {
    title: '常見問題',
    description: '有疑問？請先查看以下常見問題解答，或聯絡我們的客服。',
    items: {
      item_10: {
        label: '使用 AI 擇優翻譯有什麼優點？',
        content:
          '- 不同的 AI 翻譯模型所使用的模型架構、訓練資料、訓練演算法、生成策略等各有不同，每個 AI 翻譯模型都會有其獨特的優點和缺點。因此，單一翻譯模型難以在不同語種、不同語句、不同情境下都能達到最優的翻譯效果。   \n - 精挑翻譯首創 __擇優翻譯__ 功能，同時調用多個 AI 翻譯模型進行翻譯，再用較強的 AI 模型對多個翻譯結果進行評分擇優，有效排除明顯錯誤的翻譯結果，並 __讓每一句原文都能獲得最優的譯文__ 🎯。  \n - 沒有對比就沒有傷害，歡迎親自體驗 👉 [AI擇優翻譯](text)'
      },
      item_20: {
        label: '我可以免費使用精挑翻譯嗎？',
        content:
          '- 是的，您可以完全免費地使用精挑翻譯的所有功能。有多款免費翻譯模型可以直接使用，其它翻譯模型您可以配置 API Key 後使用（[各模型 API Key 申請及配置](docs/service/configure)）。   \n - 如果你覺得逐一申請和配置翻譯模型的 API Key 比較麻煩，您也可以直接購買我們的 Plus 會員套餐服務。Plus 會員可以直接使用 DeepSeek、OpenAI、Gemini、智譜AI、豆包、通義千問 等套餐中包含的高級翻譯模型。'
      },
      item_30: {
        label: 'Plus 會員提供的 DeepSeek、OpenAI、Gemini、智譜AI、豆包、通義千問 高级模型分別使用哪個模型版本？',
        content:
          '- Plus 會員可以直接使用 DeepSeek、OpenAI、Gemini、智譜AI、豆包、通義千問 高級翻譯模型。高級模型支援多版本自選，您可以根據場景選擇適合的版本。  \n - 各高階模型預設推薦的版本如下：  \n   1、DeepSeek：DeepSeek-V3-0324  \n   2、OpenAI： GPT-4.1-mini   \n  3、Gemini： Gemini-2.5-Flash   \n  4、智譜AI： GLM-4-Plus    \n  5、豆包： Doubao-1.5-Pro-32k   \n   6、通義千問： Qwen-Plus'
      },
      item_35: {
        label: '我經常有重要的文案需要翻譯，Plus 會員可以選擇模型的高級版本來翻譯嗎？',
        content: '- 當然可以！進階版本雖成本較高，但翻譯精準度更優，尤其適用於重要文案或場景的翻譯。 Plus 會員的高級模型支援多版本自選，在模型設定中按需選用則可。'
      },
      item_40: {
        label: 'Plus 會員套餐中每月翻譯額度是多少？',
        content: '- Plus 會員套餐中每月翻譯額度为 __400萬 TQ__  \n &nbsp;   \n TQ 與 Token 的換算規則請看：[《翻譯額度與模型Token的換算規則》](docs/translation_quota)'
      },
      item_45: {
        label: '400萬 TQ 的翻譯額度相當於翻譯模型多少個Token？',
        content:
          '- __400萬 TQ__ 翻譯額度約等於各高階模型預設版本可用於翻譯的 Token 數：  \n  1、 DeepSeek-V3 ： __1000萬 Token__  \n  2、GPT-4.1-mini ： __685萬 Token__   \n   3、Gemini-2.5-Flash ： __1800萬 Token__  \n  4、智谱 GLM-4-Plus ： __800萬 Token__   \n   5、豆包 Doubao-1.5-Pro-32k ： __3300萬 Token__  \n  6、通義千問 Qwen-Plus ： __3300萬 Token__  \n &nbsp;   \n TQ 與 Token 的換算規則請看：[《翻譯額度與模型Token的換算規則》](docs/translation_quota)'
      },
      item_50: {
        label: '翻譯額度是所有高級模型共用的嗎？',
        content: '- 是的，翻譯額度是 Plus 會員支援的所有高級模型共用的。'
      },
      item_60: {
        label: 'Plus 會員可以免費試用嗎？',
        content: '- 可以，註冊帳號並登入，可免費申請試用 Plus 會員【 3 】天。  \n - 試用套餐有翻譯額度限制，試用額度用完或試用期結束，你可以選擇購買/訂閱正式版的 Plus 會員套餐。'
      },
      item_70: {
        label: '可以用銀聯的信用卡訂閱或購買 Plus 會員嗎？',
        content:
          '- 可以，選擇 Stripe 支付，支援使用 銀聯 ![pay_card](assets/images/logos/pay_unionpay.svg)、Visa ![pay_card](assets/images/logos/pay_visa.svg)、萬事達 ![pay_card](assets/images/logos/pay_mastercard.svg) 在內的所有主流信用卡機構發行的信用卡進行訂閱或購買。'
      },
      item_75: {
        label: '可以開立中國大陸的增值稅普通發票嗎？',
        content:
          '- 如果你需要開立中國大陸的增值稅普通發票，請使用【微信支付】或【支付寶】支付購買，然後將以下開票資訊發送到服務郵箱 service&#64;selecttranslate.com ，我們將及時給您確認並開票。  \n __開立發票資訊__： 帳戶ID、服務訂單號碼、發票抬頭、統一社會信用代碼/納稅人識別號碼、接收發票的電子郵件信箱  \n - __請注意__： 如果你使用 Stripe 支付，則無法申請開立中國大陸增值稅普通發票。但 Stripe 會向您的郵箱發送帳單（Invoice）和收據（Receipt），大部份企業是支援使用 Stripe 的收據作為報銷憑證的，您可以與財務部門確認是否可以使用 Stripe 的收據作為報銷憑證。'
      },
      item_80: {
        label: 'Plus 會員套餐翻譯額度不夠用怎麼辦？',
        content:
          '- Plus 會員套餐中的每月翻譯額度是可以滿足絕大部分用戶的需求。  \n 如果每月套餐翻譯額度不夠用，我們還提供多種規格的翻譯額度加量包。您可以按需要[購買翻譯額度加量包](resource?type=general_token)。'
      },
      item_90: {
        label: '購買了 Plus 會員套餐，可以申請退款嗎？',
        content:
          '- __按月訂單/訂閱__：首次購買後 24 小時內可申請退款。  \n - __按季度訂單/訂閱__：首次購買後 48 小時內可申請退款。   \n - __年度訂單/訂閱__：首次購買後 72 小時內可申請退款。   \n &nbsp;  \n 請注意，超過申請期限的申請將不予退款。申請退款時，請說明申請退款的原因。  \n 只有在訂單對應的翻譯額度未被大量使用的情況下才可退款。如果訂單對應的翻譯額度已被大量使用，您的退款申請可能會被拒絕。   \n 如果您之前已成功申請並獲得我們服務的退款，我們保留拒絕您任何其他退款要求的權利。   \n 我們將根據具體使用情況審核退款申請，確保使用者能公平使用我們的服務，以防止退款規則被濫用。  \n &nbsp;  \n - __退款流程__：如果您符合上述退款條件，請將退款申請寄至我們的服務信箱 service&#64;selecttranslate.com 。我們的服務團隊將審核您的申請，並會在 7 個工作天內處理並通知您。'
      },
      item_100: {
        label: '不想再續訂 Plus 會員套餐，如何取消訂閱？',
        content: '- 登入帳號後，進入【帳戶】頁面，在【當前套餐】中點選【取消訂閱】按鈕，按提示操作即可取消訂閱。'
      },
      item_110: {
        label: '會員帳號有使用設備數量限制嗎？',
        content: '- 有，每個會員帳號最多可同時在 10 個裝置（瀏覽器）上使用，超出限制數量時，最早登入的裝置將會被登出。'
      },
      item_120: {
        label: '我想提一些產品功能改進和創新的建議，如何反饋？',
        content:
          '- 非常歡迎用戶能向我們反饋產品使用上遇到的問題，也非常期待用戶能結合自己的需求向我們提供更多產品功能改進和創新的建議。  \n - 您可以點擊頁面底部的【[問題反饋](feedback)】鏈接，即可進入反饋/建議表單。  \n - 您的反饋和建議對我們來說非常寶貴，我們會認真閱讀每一個反饋和建議並及時回覆。您可以在帳戶中心的【[問題反饋清單](feedback_list)】中查看回覆並與我們進行溝通互動。'
      }
    }
  },
  faq_resource_pack: {
    items: {
      item_210: {
        label: '翻譯額度加量包的適用範圍？',
        content: '- 翻譯額度加量包適用於 Plus 會員支援的所有進階翻譯模型，包括 DeepSeek、OpenAI、Gemini、智譜AI、豆包、通義千問。'
      },
      item_220: {
        label: '只有 Plus 會員才能購買翻譯額度加量包？',
        content: '- 是的，只有 Plus 會員才能購買和使用翻譯額度加量包。'
      },
      item_230: {
        label: '翻譯額度加量包的有效期限是多久？',
        content: '- 加量包翻譯額度在 Plus 會員服務生效期間永久有效。如果 Plus 會員服務中途到期了，還有未使用的加量包翻譯額度，可以在續費 Plus 會員後繼續使用。'
      },
      item_240: {
        label: 'Plus 會員套餐的每月翻譯額度和加量包中的翻譯額度使用優先順序？',
        content: '- 會優先使用 Plus 會員套餐的每月翻譯額度，只有套餐每月翻譯額度使用完後，才會使用加量包中的翻譯額度。'
      }
    }
  },
  check_installed: {
    // 检查是否安装插件
    extension_name: '精挑翻譯擴充功能',
    add_browser_1: '添加到',
    add_browser_2: '免費添加到',
    other_browsers: '其他瀏覽器',
    extension_not_installed: {
      tip_1: '您的瀏覽器【未安裝】或【未啟用】',
      tip_2: '請安裝並啟用擴充功能後重試',
      tip_3: '安裝並啟用精挑翻譯插件後即可馬上體驗！'
    },
    extension_version_too_low: {
      tip_1: '偵測到您的',
      tip_2: '版本過低',
      tip_3: '請升級擴充功能至最新版本後重試'
    }
  },
  extensions: {
    // crx 包安装
    crx: {
      title: '精挑翻譯 Chrome 擴充功能下載安裝教程',
      download_button: '下載 crx 安裝包',
      download_package: {
        title: '1. 下載擴充功能',
        description: '點選上面的【下載 crx 安裝包】按鈕，下載精挑翻譯的 Chrome 擴充功能 zip 壓縮包，解壓縮後即可得到擴充功能的 crx 安裝檔。'
      },
      chrome_extensions_dev_mode: {
        title: '2. 擴充功能管理設置',
        description: '在 Chrome 瀏覽器網址列輸入 chrome://extensions 並回車，進入擴充功能管理介面，在右上角開啟【開發人員模式】。'
      },
      install_step1: {
        title: '3. 拖入安裝擴充功能',
        description: '把擴充功能的 crx 安裝文件，拖入到擴充功能管理介面。'
      },
      install_step2: {
        title: '4. 新增擴充功能',
        description: '會彈跳窗提示新增"精挑翻譯"，點擊【新增擴充功能】按鈕即可完成安裝。'
      },
      install_step3: {
        title: '5. 啟用擴充功能',
        description: '確保擴充功能處於【啟用】狀態，如左圖步驟設置，將精挑翻譯固定到擴充功能導覽欄，方便後續使用。'
      },
      faq: {
        title: 'crx 方式安裝常見問題',
        desc: '有疑問？請查看下面的常見問題，或聯絡我們的客服。',
        items: {
          item1: {
            title: '將 crx 檔案拖入擴展程式管理介面沒反應，或提示已封鎖 - 不明來源。',
            desc: '請檢查以上第2步驟操作：在瀏覽器網址列輸入 chrome://extensions 並回車，進入擴充功能管理介面，在右上角開啟【開發人員模式】，重新拖曳到 crx 檔案即可。'
          },
          item2: {
            title: '透過 "載入未封裝項目" 安裝沒有可選的文件，或提示 "無法載入擴充功能"。',
            desc: '注意：必須以拖入 crx 檔案的方式安裝。不要透過 "載入未封裝項目" 導入 crx，也不要雙擊 crx 檔案。'
          },
          item3: {
            title: '瀏覽器不支援 crx 檔案。',
            desc: '如果瀏覽器不支援 crx 檔案安裝，可使用 zip 檔案方式安裝。'
          }
        }
      }
    },
    // zip拓展程序
    zip: {
      title: '精挑翻译 擴充功能Zip安装教程', // 精挑翻译 擴充功能Zip安装教程
      download_button: '下载 Zip 安装包', // 下载 Zip 安装包
      download_install_package: {
        title: '獲取 精挑翻譯 擴充功能安裝包', // 获取 精挑翻译 擴充功能安装包
        item1: '下載並且解壓縮zip安裝包', // 下载并且解压zip安装包
        item2: '解壓縮後的資料夾需要永久保留，否則影響使用' // 解压后的文件夹需要永久保留，否则影响使用
      },
      open_dev_model: {
        title: '開啟 開發人員模式', // 开启 开发者模式
        item1: '在網址列輸入 "chrome://extensions/" 並回車', // 在地址栏输入 "chrome://extensions/" 并回车
        item2: '進入擴充功能管理頁面，在右上角開啟 "開發人員模式"' // 进入扩展程序管理页面，在右上角开启 "开发者模式"
      },
      install_package: {
        title: '安裝 精挑翻譯 擴充功能', // 安装 精挑翻译 擴充功能
        item1: '方式一：把解壓縮出來的資料夾拖入擴充功能管理頁面', // 方式一：把解压出来的文件夹拖入扩展程序管理页面
        item2: '方式二：點選左上角 "載入已解壓縮的拓展程式"，選擇解壓縮出來的資料夾' // 方式二：点击左上角 "加载已解压的拓展程序"，选择解压出来的文件夹
      },
      enable_package: {
        title: '啟用 精挑翻譯 擴充功能', // 启用 精挑翻译 擴充功能
        item1: '點擊狀態列 "拓展程式" 按鈕', // 点击状态栏 "拓展程序" 按钮
        item2: '點擊 "固定" 即可完成設置' // 点击 "固定" 即可完成设置
      }
    }
  },
  selected_trans: {
    title: 'AI 擇優翻譯、對比翻譯、文字翻譯', // AI 择优翻译、对比翻译、文本翻译
    multiple_translation_results: '以下為該句多個翻譯結果：', // 以下为该句多个翻译结果
    auto_detect: '自動檢測',
    settings: {
      translation_models: '翻譯模型',
      original_language: '原文語言',
      target_language: '目標語言',
      auto_translate: '自動翻譯',
      history: '歷史記錄',
      custom_results: '自選結果',
      trans_models: {
        title: '對比翻譯模型', // 对比翻译模型
        selected_01: '已選擇',
        selected_02: '個模型',
        collapse: '收起', // 收起(折叠)
        expand: '顯示更多', // 显示更多(展开)
        close: '關閉', // 关闭
        target_language_is_not_supported: '不支援當前目標語言'
      },
      ai_selected_translation: 'AI 擇優翻譯',
      ai_selected_translation_desc: '基於對比翻譯，AI 自動評分多個翻譯結果，智慧排序並選出最優譯文。',
      selected_mode: '逐句擇優',
      selected_mode_desc: '啟用：逐句擇優，關閉：全文擇優',
      selected_rationale: '擇優分析',
      selected_rationale_desc: '顯示擇優評分的說明（會增加翻譯額度使用量）',
      selected_model: '擇優模型',
      selected_model_desc: '對翻譯結果進行擇優評分和分析的 AI 模型'
    },
    main: {
      translating: '翻譯中...',
      scoring: '評分中...',
      scoring_and_analyzing: '擇優分析中...',
      waiting_for_optimization: '等待擇優...',
      selected_score: '擇優評分',
      ai_selected_result: 'AI 擇優結果',
      select_this_result: '選擇此結果',
      translation_exception: '翻譯異常', // 翻译异常
      copy: '複製' // 复制
    },
    operation: {
      traditional_ranslation_btn: '傳統翻譯', // 传统翻译
      compare_translation_btn: '對比翻譯',
      ai_selected_translation_btn: '擇優翻譯',
      traditional_ranslation_btn2: '傳統翻譯',
      restore: '還原',
      re_scoring: '重新擇優',
      re_translate: '重新翻譯',
      re_compare: '重新對比',
      expand: '展開',
      collapse: '隱藏'
    },
    textarea: {
      select_at_least_one_model: '請至少選擇 1 個翻譯模型',
      text_to_be_translated: '請輸入要翻譯的文本',
      ai_selected_min_models_tip: 'AI 擇優 - 需要選擇 2 個或以上翻譯模型'
    },
    history: {
      no_history: '暫無歷史紀錄', // 暂无历史记录
      search_history: '搜尋歷史記錄', // 搜索历史记录
      clear_history: '清空歷史', // 清空历史
      clear_history_confirm: '確認要清空所有歷史記錄？', // 确认要清空所有历史记录吗？
      by_sentence: '逐句擇優', // 按句
      by_fulltext: '全文擇優', // 全文
      today: '今天',
      yesterday: '昨天'
    },
    error_info: {
      original_text: '原文內容',
      no_original_text: '沒有原文內容',
      retry: '重試',
      retry_all: '重試全部'
    },
    error: {
      text_length_exceeded: '文本長度超出限制',
      text_length_exceeded_desc: '文本長度不能超過 {max} 個字符',
      same_language: '原文語言與目標語言相同',
      same_language_desc: '原文語言與目標語言相同，請檢查翻譯設定！'
    }
  },
  footer: {
    // 页脚
    help_enter: '幫助中心', // 帮助中心
    use_doc: '使用文檔', // 使用文档
    faq: '常見問題', // 常见问题
    feedback: '問題反饋', // 问题反馈
    terms_policies: '條款和政策', // 条款和政策
    terms_of_service: '服務條款', // 服务条款
    privacy_policy: '隱私政策', // 隐私政策
    company: '公司', // 公司
    about: '關於', // 关于
    information: '資訊', // 资讯
    wechat_official_account: '微信公眾號', // 微信公众号
    wechat_group: '微信交流群', // 微信交流群
    copyright: 'Copyright © 智應軟件有限公司版權所有' // 版权
  },
  http_service: {
    http_no_content_returned_by_backend_api: '後端 API 沒有回傳內容',
    http_operation_failed_please_retry: '操作失敗，請重試',
    http_400_request_error: '請求錯誤',
    http_401_authentication_has_expired_please_login_again: '認證已過期，請重新登入',
    http_403_no_access_permission_please_log_in_first: '請先登入',
    http_404_error_with_the_requested_url: '請求的 URL 出錯：',
    http_408_request_timeout: '請求超時',
    http_500_internal_service_error: '內部服務錯誤',
    http_501_service_not_implemented: '服務未實現',
    http_502_gateway_error: '網關錯誤',
    http_503_service_unavailable: '服務不可用',
    http_504_gateway_timeout: '網關逾時',
    http_505_http_version_not_supported: 'HTTP 版本不受支援'
  },
  coupon: {
    code: {
      title: '兌換碼',
      instructions: {
        title: '兌換說明',
        instruction_01: '1. 使用兌換碼將直接升級為 Plus 會員。',
        instruction_02: '2. 如果你已經是 Plus 會員，使用兌換碼將延長 Plus 會員有效期限。',
        instruction_03: '3. 如果你獲得的是指定發放的兌換碼，請在 "我的兌換碼" 清單中尋找 "未使用" 的兌換碼並 "兌換"。',
        instruction_04: '4. 兌換碼存在有效期，過期無法兌換，請盡快使用。',
        instruction_05: '5. 本內容為虛擬產品，兌換後不可退款。'
      },
      use_title: '使用兌換碼',
      input_tips: '請輸入兌換碼',
      redeem: '兌換',
      redeem_success: '兌換成功',
      my_code: '我的兌換碼',
      table: {
        query: {
          use_all: '全部',
          use_unused: '未使用',
          use_used: '已使用'
        },
        redemption_code: '兌換碼',
        product_specification: '規格',
        usage_type: '使用類型',
        claim_time: '領取時間',
        redemption_start_time: '兌換開始時間',
        redemption_end_time: '兌換結束時間',
        redemption_time: '兌換時間',
        status: '狀態',
        operation: '操作',
        redeem: '兌換' // 兑换
      }
    }
  },
  feedback: {
    title: '問題反饋',
    form: {
      header: '創建 問題 / 建議',
      type: '類型',
      type_options: {
        feedback: '問題反饋',
        suggestion: '功能建議'
      },
      content: {
        label: '內容',
        placeholder: '請輸您要回饋/建議的內容（500字以內）'
      },
      screenshot: {
        label: '截圖',
        upload_text: '點擊或拖拽到此處添加圖片',
        drop_text: '釋放滑鼠上傳圖片'
      },
      contact: {
        label: '聯繫方式',
        placeholder: '可提供：郵箱 / 微信 / QQ等，以方便我們快速聯繫'
      },
      submit: '提交',
      submitting: '提交中...',
      wechat_group_tip: '建議加入微信交流群快速回饋'
    },
    image_viewer: {
      title: '圖片預覽'
    },
    messages: {
      upload_limit_exceeded: '超出最大上傳限制',
      max_images: '最多只能上傳{count}張圖片',
      invalid_file_type: '檔案格式錯誤',
      only_jpg_png: '只能上傳JPG或PNG格式圖片',
      file_too_large: '檔案過大',
      max_file_size: '圖片大小不能超過3MB',
      content_required: '請輸入反饋內容',
      success: '成功',
      submit_with_images_success: '您的反饋和圖片已成功提交！',
      image_upload_failed: '圖片上傳失敗',
      partial_success: '反饋已提交，但圖片上傳失敗',
      submit_success: '您的反饋已成功提交！客服會盡快回覆您。',
      submit_failed: '提交失敗',
      unknown_error: '未知錯誤'
    },
    list: {
      title: '我的回饋',
      columns: {
        no: '編號',
        type: '類型',
        time: '時間',
        content: '內容',
        status: '狀態',
        action: '操作'
      },
      status: {
        pending: '待處理',
        processing: '處理中',
        completed: '處理完成'
      },
      type: {
        suggestion: '功能建議',
        feedback: '問題反饋'
      },
      actions: {
        view_reply: '查看回覆',
        images: '張',
        create_feedback: '創建反饋'
      },
      empty: '您還沒有反饋',
      errors: {
        no_id: '無法跳轉，未找到有效的ID',
        navigation_failed: '跳轉失敗',
        missing_feedback_no: '缺少必要的反饋編號信息'
      }
    }
  },
  reply_list: {
    title: '問題反饋回覆',
    feedback_details: {
      number: '編號',
      time: '時間',
      type: '類型',
      status: '狀態',
      content: '內容',
      images: '圖片',
      no_images: '無圖片'
    },
    images: {
      count: '張',
      view_all: '查看全部'
    },
    reply: {
      user: '用戶',
      manager: '售後經理',
      button: '回覆',
      cancel: '取消',
      submit: '提交',
      content_label: '內容',
      content_placeholder: '請輸入訊息，支援圖片貼上及拖曳上傳',
      images_label: '截圖',
      upload_text: '點擊或拖拽到此處添加圖片',
      drop_text: '釋放滑鼠上傳圖片'
    },
    status: {
      completion: '是否完結',
      mark_as_completed: '標記為已完成',
      completed: '已完結',
      not_completed: '未完結',
      update_success: '更新成功',
      update_success_message: '問題狀態已更新為處理完成',
      update_failed: '更新失敗',
      update_failed_message: '無法更新反饋狀態',
      confirm_title: '確認提示',
      confirm_complete: '如果您回饋的問題已解決，可以點選「確認」以關閉該問題。問題關閉後可能無法再收到回复，但您仍可以隨時建立新的回饋。',
      confirm: '確定',
      cancel: '取消'
    },
    loading: {
      loading_more: '正在加載更多回覆...',
      all_loaded: '已載入所有',
      waiting: '回覆中'
    },
    messages: {
      limit_exceeded: '超出最大上傳限制',
      max_images: '最多只能上傳{count}張圖片',
      invalid_file_type: '檔案格式錯誤',
      only_jpg_png: '只能上傳JPG或PNG格式圖片',
      file_too_large: '檔案過大',
      max_file_size: '圖片大小不能超過3MB',
      content_required: '請輸入回覆內容',
      success: '成功',
      submit_success: '您的回覆已成功提交！客服會盡快回覆您。',
      image_upload_failed: '圖片上傳失敗',
      partial_success: '回覆已提交，但圖片上傳失敗。',
      missing_data: '缺少問題數據',
      submit_failed: '提交失敗',
      unknown_error: '未知錯誤',
      file_input_error: '檔案選擇器無法開啟',
      reload_or_use_drag: '請嘗試重新整理頁面或改用拖曳方式上傳圖片'
    }
  },
  error_info: {
    // 错误信息
    retry: '重試' // 重试
  },
  member_message: {
    title: '消息通知', // 消息通知
    tabs: {
      all_messages: '全部消息', // 全部消息
      unread_messages: '未讀消息' // 未读消息
    },
    toast: {
      mark_read: '標記已讀', // 标记已读
      succeed: '成功', // 成功
      mark_read_success: '標記已讀成功', // 标记已读成功
      prompt: '提示', // 提示
      prompt_content: '請選擇要標記已讀的消息', // 请选择要标记已读的消息
      prompt_failed: '標記失敗', // 标记失败
      prompt_failed_content: '標記失敗，請重試', // 标记失败，请重试
      loading_failed: '加載失敗', // 加载失败
      loading_failed_content: '加載失敗，請重試', // 加载失败，请重试
      get_message_failed: '獲取消息失敗', // 获取消息失败
      get_message_failed_content: '請檢查網絡連接或稍後再試', // 请检查网络连接或稍后再试
      delete_success: '刪除成功', // 删除成功
      delete_success_content: '消息已刪除', // 消息已删除
      delete_failed: '刪除失敗', // 删除失败
      delete_failed_content: '刪除失敗，請重試', // 删除失败，请重试
      delete_prompt_content: '未選中任何消息', // 未选中任何消息
      delete_title: '刪除消息', // 删除消息
      delete_tip: '確認刪除選定消息嗎？此操作不可逆，請慎重考慮！' // 确认删除选定消息吗？此操作不可逆，请慎重考虑！
    },
    loading: {
      loading_message: '正在加載消息...' // 正在加载消息...
    },
    read: '已讀', // 已读
    unread: '未讀', // 未读
    message_status: '消息狀態', // 消息状态
    message_title: '消息標題', // 消息标题
    create_datetime: '接收時間', // 接收时间
    message_preview: '消息預覽', // 消息预览
    action: '操作', // 操作
    delete: '刪除', // 删除
    more_actions: '更多操作' // 更多操作
  },
  translate_type: {
    text: {
      title: '擇優翻譯',
      description: '傳統翻譯、AI 擇優翻譯'
    },
    file: {
      title: '文件翻譯',
      description: '支援多種文件格式'
    },
    miner_u: {
      title: 'MinerU 文件翻譯',
      description: '精確解析複雜格式內容',
      display_mode: {
        original_text_comparison: '原文對照模式',
        results_focus: '結果聚焦模式'
      }
    },
    pdf_plus: {
      title: 'PDF Plus',
      description: '專業 PDF 文件翻譯，公式、圖表高保真還原'
    }
  },
  document_translation: {
    // 文档翻译
    title: '文件翻譯',
    description: '免費的文件翻譯功能，支援PDF文件、Markdown文件、HTML文件、text文件等多種格式文件的翻譯。',
    pdf_bilingual_translation: 'PDF 雙語對照翻譯',
    pdf_bilingual_translation_desc: '完全免費的PDF 翻譯功能，逐頁雙語對照，日常外語文件快速翻譯，滿足大部分PDF 翻譯閱讀需求。',
    open_file: '打開文件',
    open_new_file: '打開新文件',
    select_new_file: '選擇新文件',
    open_file_tips: '支援 pdf、epub、txt、html、docx、md、srt、ass 等格式文件',
    // DOCX 文档翻译
    docx_title: 'Word 文件翻譯',
    docx_description: '免費的 Word 文件翻譯功能，支援 .doc、.docx 格式文件的智能解析和翻譯。',
    parsing_document: '正在解析文件...',
    parsing_progress: '解析進度',
    parse_error: '解析失敗',
    parse_failed: '文件解析失敗',
    invalid_file_type: '不支援的文件格式，請上傳 .doc 或 .docx 文件',
    file_too_large: '文件過大，請上傳小於 50MB 的文件',
    upload_link_failed: '獲取上傳連結失敗',
    query_failed: '查詢解析狀態失敗',
    content_empty: '解析內容為空',
    download_url_missing: '下載連結缺失',
    parse_timeout: '解析超時，請稍後重試',
    single_column: '單欄模式',
    split_view: '分欄模式',
    show_original: '顯示原文',
    show_translation: '顯示翻譯',
    original_text: '原文',
    translated_text: '翻譯',
    select_view_mode: '請選擇顯示模式',
    sync_scroll: '同步滾動',
    split_ratio: '分欄比例',
    export_file: '導出文件',
    export_epub: '導出 EPUB',
    export_html: '導出 HTML',
    export_txt: '導出 TXT',
    export_markdown: '導出 Markdown',
    export_docx: '導出 Word',
    export_pdf: '導出 PDF',
    cancel_translation: '取消翻譯',
    start_translation: '開始翻譯',
    bilingual_translation: '雙語對照翻譯',
    fulltext_translation: '全文翻譯（僅譯文）',
    pdf: {
      label: 'PDF 檔案',
      open_file_tip: '開啟 pdf 檔案開始翻譯'
    },
    ppt: {
      // title: 'PPT 文件翻译',
      label: 'PPT 檔案'
      // open_file_tip: '打开 ppt/pptx 文件开始翻译'
    },
    image: {
      label: '圖片'
    },
    epub: {
      title: '電子書 EPUB 翻譯',
      label: 'EPUB 檔案',
      open_file_tip: '打開 epub 文件開始翻譯'
    },
    html: {
      title: 'HTML 檔案翻譯',
      label: 'HTML 檔案',
      open_file_tip: '打開 html/txt 文件開始翻譯'
    },
    txt: {
      label: '文本檔案'
    },
    markdown: {
      title: 'Markdown 檔案翻譯',
      label: 'Markdown 檔案',
      open_file_tip: '打開 markdown 文件開始翻譯'
    },
    docx: {
      title: 'Docx 檔案翻譯',
      label: 'Docx 檔案',
      open_file_tip: '打開 doc/docx 文件開始翻譯'
    },
    subtitle: {
      open_file_tip: '打開 srt/vtt/ass/ssa/sbv/lrc 檔案開始翻譯',
      title: '字幕檔案翻譯',
      label: '字幕檔案',
      export_trans_subtitle: '導出譯文字幕',
      export_bilingual_subtitle: '導出雙語字幕',
      viewer_table_head_start_time: '開始時間',
      viewer_table_head_end_time: '結束時間',
      viewer_table_head_content: '內容',
      viewer_table_head_translation: '譯文（可編輯）',
      please_select_subtitle_file: '請選擇 字幕 檔案',
      unsupported_subtitle_file_format: '不支持的字幕檔案格式',
      failed_read_subtitle_file: '字幕檔案讀取失敗',
      subtitle_file_content_error: '未解析到任何字幕內容，請檢查檔案內容',
      export_settings: {
        title: '導出字幕設置',
        subtitle_order: '字幕順序：',
        translation_first: '譯文在前',
        original_first: '原文在前',
        subtitle_track: '字幕軌道：',
        single_track: '單轨',
        double_track: '雙軌'
      },
      miner_u: {
        label: 'MinerU'
      },
      pdf_plus: {
        label: 'PDF Plus'
      }
    }
  },
  free_pdf_translation: {
    title: 'PDF 文檔翻譯',
    description: '完全免費的 PDF 雙語對照翻譯功能，滿足您大部分外語 PDF 文件的翻譯閱讀需求。',
    open_a_new_pdf_file: '開啟新 PDF 文件',
    empty_state_title: '開啟新 PDF 文件開始翻譯',
    empty_state_description: '拖曳檔案到此處或點擊選擇檔案',
    select_file: '選擇檔案',
    drop_file_here: '釋放文件到此處',
    translate_all_and_download: '翻譯全部並下載',
    please_select_pdf_file: '請選擇 PDF 文件',
    download_pdf_dialog_title: '下載文件',
    download_pdf_dialog_description: '下載前請先檢查翻譯效果及輸出格式',
    translated: '已翻譯', // 已翻译
    the: '第', // 第
    page: '頁',
    pages: '頁', // 页
    total: '總計', // 总计
    translate_all_pages_before_downloading: '翻译所有页面后再下载',
    translation_mode: '翻譯模式', // 翻译模式
    export_format: '導出格式', // 导出格式
    bilingual_translation: '雙語對照（灰色背景）', // 双语对照（灰色背景）
    fulltext_translation: '全文翻譯（僅譯文）', // 全文翻译（仅译文）
    text_based_pdf: '文字版 PDF', // 文字版 PDF
    image_based_pdf: '圖片版PDF', // 图片版 PDF
    text_based_pdf_download_tips_1: '文字版PDF 下載',
    text_based_pdf_download_tips_2: '點選【下載】會顯示瀏覽器的列印介面，在【目的地】中選擇【另存為 PDF】，點選【儲存】即可。',
    download_pdf_dialog_tips: '注意：建議避免下載超過300 頁或內容過大的PDF 文件，以免因係統資源不足而無法下載。',
    download: '下載', // 下载
    no_content_to_print: '沒有需要打印的內容', // 没有需要打印的内容
    translating_please_wait: '正在翻譯，請勿關閉窗口...',
    downloading_please_wait: '正在下載，請勿關閉窗口...',
    loaded: '已載入'
  },
  mineru_doc_translation: {
    title: 'MinerU 文件翻譯', // MinerU 文件翻译
    description: 'MinerU 文件翻譯，使用 MinerU 精確解析提取文檔中的文本、圖片、表格、公式等元素內容，再由精挑翻譯對解析後的文本進行智能翻譯。文檔在線瀏覽與文檔解析翻譯完美結合，為您提供極致的文檔翻譯體驗！'
  },
  pdf_plus: {
    title: 'PDF Plus（專業 PDF 翻譯）', // PDF Plus（专业 PDF 翻译）
    description: '專業 PDF 文件翻譯，公式、圖表高保真還原', // 专业 PDF 文档翻译，公式、图表高保真还原
    please_log_in_first: '請先登入', // 请先登录
    upload_file: '上傳文件', // 上传文件
    upload_file_tips: '請上傳 PDF 格式文件', // 请上传 PDF 格式文件
    upload_file_name: '文件名', // 文件名
    pages: '頁', // 页
    total: '總計', // 总计
    translation_model: '翻譯模型', // 翻译模型
    target_language: '目標語言', // 目标语言
    translation_mode: '譯文顯示', // 译文显示
    page_range: '頁面範圍', // 页面范围
    specify_page_range: '指定頁面範圍', // 指定页面范围
    specify_page_range_tips_1: '不設定，則預設翻譯整個PDF',
    specify_page_range_tips_2: '指定頁碼，使用英文逗號分隔：1,3,-1',
    specify_page_range_tips_3: '使用"-"指定範圍，如第1頁到第5頁：1-5',
    specify_page_range_tips_4: '第5頁到倒數第3頁：5--3',
    specify_page_range_tips_5: '倒數第5頁到倒數第2頁：-5--2',
    specify_page_range_tips_6: '組合使用頁碼及範圍，使用英文逗號分隔：1,3,5-8,-4--2',
    exceeds_pdf_plus_limit_tips_1: '要翻譯頁數', // 要翻译页数 AA
    exceeds_pdf_plus_limit_tips_2: '頁，超過了您的PDF Plus 剩餘頁數', // 页，超过了您的 PDF Plus 剩余页数
    exceeds_pdf_plus_limit_tips_3: '頁', // 页
    page_range_limit_tips_1: '目前檔案僅有', // 当前文件仅有 XX
    page_range_limit_tips_2: '頁，請輸入正確頁碼範圍', // 页，请输入正确页码范围
    translate_now: '立即翻譯', // 立即翻译
    pdf_plus_remaining_pages_tips_1: 'PDF Plus 剩餘頁數：本月會員套餐', // PDF Plus 剩余页数：本月会员套餐
    pdf_plus_remaining_pages_tips_2: '頁，加量包', // 页，加量包
    pdf_plus_remaining_pages_tips_3: '頁', // 页
    purchase_add_on_package: '購買加量包', // 购买加量包
    parsing_the_file: '正在解析文件...', // 正在解析文件...
    bilingual_translation: '雙語對照（灰色背景）', // 双语对照（灰色背景）
    fulltext_translation: '全文翻譯（僅譯文）', // 全文翻译（仅译文）
    messages: {
      parsing_failed: '解析失敗',
      parsing_failed_tips_1: '上傳檔案大小不能超過100MB',
      parsing_failed_tips_2: '無法解析文件- 文件可能已損壞或加密方式不受支持',
      parsing_failed_tips_3: '無法解析加密文件',
      parsing_failed_tips_4: '請檢查文件', // 请检查文件
      loading_failed: '載入失敗', // 加载失败
      loading_failed_tips_1: '該檔案已加密，請解密後重新上傳',
      loading_failed_tips_2: '產品/會員服務已過期，請續費後重試'
    },
    back: '返回' // 返回
  },
  pdf_plus_record: {
    // PDF 解析记录
    title: 'PDF 解析記錄', // PDF 解析记录
    table: {
      index: '序號', // 序号
      file_name: '檔案名稱', // 文件名称
      parsed_page_count: '頁數', // 页数
      create_datetime: '時間', // 时间
      parsing_status: '狀態', // 状态
      actions: '操作' // 操作
    },
    parsing_status: {
      unparsed: '未解析',
      parsing: '解析中',
      failed: '解析失敗', // 解析失败
      success: '解析成功'
    },
    view: '查看',
    delete: '刪除', // 删除
    delete_confirm_title: '刪除記錄', // 删除记录
    delete_confirm_content: '確認刪除選取記錄嗎？', // 确认删除选中记录吗？
    delete_success: '刪除成功',
    delete_success_tips: '已刪除' // 已删除
  },
  pdf_plus_introduction: {
    // PDF Plus 介绍
    title: 'AI 驅動的專業 PDF 文件翻譯',
    description: ' PDF Plus 基於領先的AI 視覺處理技術，能精確解析PDF 文件或圖片中的手寫文字、數學方程式、複雜表格、程式碼片段、化學公式等內容，適用於學術論文和各類專業文件的翻譯。',
    parsing_type: {
      // 解析类型
      text_and_handwriting: '文字和手寫內容',
      text_and_handwriting_desc: '智慧擷取圖片和PDF 中的印刷文字和手寫內容。',
      mathematical_equations: '數學公式',
      mathematical_equations_desc: '採用博士級的數學演算法，精確解析圖片和文件中的方程式。',
      complex_tables: '複雜表格',
      complex_tables_desc: 'AI 視覺處理，輕鬆應付複雜表格。',
      code_snippets: '程式碼片段',
      code_snippets_desc: '從影像和PDF 中提取格式完美的程式碼片段。',
      chemical_formulas: '化學公式',
      chemical_formulas_desc: '基於OCR 技術，識別並提取化學公式和圖表。'
    },
    features: {
      bilingual_translation: '逐段雙語對照',
      bilingual_translation_desc: '在每段原文後以鮮明的格式顯示譯文，逐段對比，清晰易讀。',
      supports_scanned_versions: '支援掃描版 PDF',
      supports_scanned_versions_desc: '採用 AI + OCR（光學字元辨識）技術，支援掃描版PDF 或文件中圖片格式內容的精確解析翻譯。',
      layout_conversion: '雙欄、三欄佈局轉換',
      layout_conversion_desc: '針對學術論文常用的雙欄、三欄佈局，統一轉換為易於閱讀的單欄格式，實現更優雅的內容排版，提升閱讀體驗。'
    },
    export: {
      title: '匯出翻譯內容', // 导出翻译内容
      html: '匯出為 HTML',
      html_desc: '翻譯後的內容可以 HTML 格式匯出儲存，方便在不同裝置的瀏覽器中閱讀。',
      pdf: '匯出為 PDF',
      pdf_desc: '翻譯後的內容可以 PDF 格式匯出儲存，並將原樣保留線上翻譯閱讀時的排版和樣式。'
    }
  },
  tutorial: {
    table_of_contents: '內容目錄'
  },
  safari_guide: {
    title: '擴充初始化設定',
    description: '快速了解如何在 Safari 瀏覽器中啟用精挑翻譯擴展',
    previous_step: '上一步', // 上一步
    next_step: '下一步', // 下一步
    get_started: '開始使用', // 开始使用
    step1: {
      title: '🎉 歡迎！現在啟用插件',
      description: '精挑翻譯 Safari 擴充功能已經成功安裝，現在需要啟用它並授予必要的權限。',
      content: {
        click_icon: '1. 點選搜尋框左側圖標',
        click_manage_extensions: '2. 點選選項裡的「管理擴充」',
        open_selecttranslate: '3. 開啟「精挑翻譯」'
      }
    },
    step2: {
      title: '允許擴充訪問網站',
      description: '請選擇"允許"以確保在所有網站上獲得翻譯功能',
      content: {
        click_selecttranslate: '1. 點擊選項裡的「精挑翻譯」',
        select_always: '2. 選擇「永遠允許」',
        select_always_website: '3. 選擇「一律在每個網站允許」'
      }
    },
    step3: {
      title: '體驗網頁翻譯',
      article: {
        title: 'Digital Life in the Modern World'
      }
    }
  },
  doc_trans_progress: {
    trans_progress: '翻譯進度',
    doc_trans_progress: '文檔翻譯進度',
    the: '第',
    page: '頁',
    translated: '已翻譯',
    translation_in_progress: '翻譯中',
    no_translation_needed: '無需翻譯',
    waiting: '等待中',
    failed_nodes: '處理失敗',
    failed: '失敗',
    total: '共'
  },
  // 欢迎页
  welcome: {
    title: '歡迎使用精挑翻譯',
    description: '下面透過幾個步驟快速了解我們的核心功能 👇',
    tooltip_pc: '向下滾動開始探索', // 桌面端描述
    tooltip_mobile: '向下滑動開始探索', // 移动端描述
    click_floating_button: '你可以通過點擊右側的懸浮按鈕', // 你可以通过点击右侧的悬浮按钮
    to_translate: '進行翻譯', // 进行翻译
    button: {
      experience: '立即體驗',
      go_experience: '前往體驗'
    },
    more_translation_features: {
      title: '更多翻譯功能',
      description: '深入應用場景，全新的翻譯體驗，等您來探索！'
    }
  }
}
