# PDF.js offsetParent 错误修复总结

## 问题描述

在 PDF 文件加载过程中，控制台出现以下错误：
```
viewer.mjs:143 offsetParent is not set -- cannot scroll
```

这个错误发生在 PDF.js 尝试滚动到特定页面位置时，但此时 PDF 页面的 DOM 元素还没有正确的 `offsetParent` 属性。

## 错误调用栈分析

```
scrollIntoView @ viewer.mjs:143
#scrollIntoView @ viewer.mjs:13006
#resetCurrentPageView @ viewer.mjs:13169
_setCurrentPageNumber @ viewer.mjs:12401
scrollPageIntoView @ viewer.mjs:13198
#setScaleUpdatePages @ viewer.mjs:13056
#setScale @ viewer.mjs:13158
set currentScaleValue @ viewer.mjs:12466
setInitialView @ viewer.mjs:16716
```

## 根本原因

1. **时序问题**：PDF 页面在 DOM 完全准备好之前就尝试执行滚动操作
2. **容器查找失败**：当 `offsetParent` 为 `null` 时，原代码直接返回错误，没有备选方案
3. **初始化竞态条件**：容器可见性检查不够严格，可能在容器还未完全渲染时就开始初始化

## 修复方案

### 1. 增强 scrollIntoView 函数 (viewer.mjs:140-164)

**修复前：**
```javascript
function scrollIntoView(element, spot, scrollMatches = false) {
  let parent = element.offsetParent
  if (!parent) {
    console.error('offsetParent is not set -- cannot scroll')
    return
  }
  // ...
}
```

**修复后：**
```javascript
function scrollIntoView(element, spot, scrollMatches = false) {
  let parent = element.offsetParent
  if (!parent) {
    // 尝试查找可滚动的父容器作为备选方案
    let fallbackParent = element.parentElement
    while (fallbackParent && fallbackParent !== document.body) {
      const style = getComputedStyle(fallbackParent)
      if (style.overflow === 'auto' || style.overflow === 'scroll' || 
          style.overflowY === 'auto' || style.overflowY === 'scroll' ||
          fallbackParent.scrollHeight > fallbackParent.clientHeight) {
        parent = fallbackParent
        break
      }
      fallbackParent = fallbackParent.parentElement
    }
    
    if (!parent) {
      // 使用 viewerContainer 作为最后的备选方案
      parent = document.getElementById('viewerContainer')
      if (!parent) {
        console.warn('offsetParent is not set and no suitable scroll container found -- cannot scroll')
        return
      }
    }
  }
  // ...
}
```

### 2. 改进偏移量计算 (viewer.mjs:165-199)

为备选父容器添加了专门的偏移量计算逻辑，使用 `getBoundingClientRect()` 来准确计算相对位置。

### 3. 增强容器可见性检查 (PdfViewer.vue:1664-1672)

**修复前：**
```javascript
const isVisible = rect.width > 0
  && rect.height > 0
  && container.offsetParent !== null
  && getComputedStyle(container).display !== 'none'
```

**修复后：**
```javascript
const computedStyle = getComputedStyle(container)
const isVisible = rect.width > 0
  && rect.height > 0
  && container.offsetParent !== null
  && computedStyle.display !== 'none'
  && computedStyle.visibility !== 'hidden'
  && computedStyle.opacity !== '0'
```

### 4. 添加页面准备状态检查 (PdfViewer.vue:1701-1732)

在文档加载完成后，等待 PDF 页面完全渲染再执行滚动操作：

```javascript
const waitForPagesReady = () => {
  const viewerContainer = document.getElementById('viewerContainer')
  const firstPage = viewerContainer?.querySelector('.page')
  
  if (firstPage && firstPage.offsetParent !== null) {
    // 页面已经准备好，可以安全地进行滚动操作
    setTimeout(() => {
      const pageNumberInput = document.getElementById('pageNumber') as HTMLInputElement | null
      if (pageNumberInput) {
        pageNumberInput.value = '1'
        pageNumberInput.dispatchEvent(new Event('change'))
      }
    }, 500)
  } else {
    // 页面还没准备好，继续等待
    setTimeout(waitForPagesReady, 100)
  }
}
```

### 5. PDF 内部滚动方法保护 (viewer.mjs:13014-13033)

在 PDF.js 内部的 `#scrollIntoView` 方法中添加保护：

```javascript
#scrollIntoView(pageView, pageSpot = null) {
  const { div, id } = pageView
  
  // 检查页面 div 是否已经正确渲染并具有 offsetParent
  if (!div || !div.offsetParent) {
    // 如果页面还没有准备好，延迟执行滚动
    setTimeout(() => {
      if (div && div.offsetParent) {
        this.#scrollIntoView(pageView, pageSpot)
      }
    }, 100)
    return
  }
  // ...
}
```

## 修复效果

1. **消除错误信息**：不再出现 "offsetParent is not set -- cannot scroll" 错误
2. **提高稳定性**：PDF 加载过程更加稳定，减少时序相关的问题
3. **保持功能**：滚动功能正常工作，用户体验不受影响
4. **向后兼容**：修复不影响现有的正常工作流程

## 测试建议

1. 在不同浏览器中测试 PDF 文件加载
2. 测试不同大小和复杂度的 PDF 文件
3. 测试在慢速网络环境下的加载情况
4. 验证页面跳转和滚动功能是否正常工作

## 注意事项

- 修复主要针对时序问题，如果 PDF 文件本身有问题，仍可能出现其他错误
- 建议在生产环境部署前进行充分测试
- 如果遇到新的相关问题，可能需要进一步调整延迟时间或检查条件
