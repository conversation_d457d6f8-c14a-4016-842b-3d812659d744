<template>
  <div class="flex-1 h-full relative">
    <div id="outerContainer" :class="{ 'reference-mode': props.mode === 'reference' }">
      <link rel="resource" type="application/l10n" href="/pdf-assets/locale/locale.json" />

      <div id="sidebarContainer">
        <div id="toolbarSidebar" class="toolbarHorizontalGroup">
          <div id="toolbarSidebarLeft">
            <div id="sidebarViewButtons" class="toolbarHorizontalGroup toggled" role="radiogroup">
              <button
                id="viewThumbnail"
                class="toolbarButton toggled"
                type="button"
                tabindex="0"
                data-l10n-id="pdfjs-thumbs-button"
                role="radio"
                aria-checked="true"
                aria-controls="thumbnailView"
              >
                <span data-l10n-id="pdfjs-thumbs-button-label" />
              </button>
              <button
                id="viewOutline"
                class="toolbarButton"
                type="button"
                tabindex="0"
                data-l10n-id="pdfjs-document-outline-button"
                role="radio"
                aria-checked="false"
                aria-controls="outlineView"
              >
                <span data-l10n-id="pdfjs-document-outline-button-label" />
              </button>
              <button
                id="viewAttachments"
                class="toolbarButton"
                type="button"
                tabindex="0"
                data-l10n-id="pdfjs-attachments-button"
                role="radio"
                aria-checked="false"
                aria-controls="attachmentsView"
              >
                <span data-l10n-id="pdfjs-attachments-button-label" />
              </button>
              <button
                id="viewLayers"
                class="toolbarButton"
                type="button"
                tabindex="0"
                data-l10n-id="pdfjs-layers-button"
                role="radio"
                aria-checked="false"
                aria-controls="layersView"
              >
                <span data-l10n-id="pdfjs-layers-button-label" />
              </button>
            </div>
          </div>

          <div id="toolbarSidebarRight">
            <div id="outlineOptionsContainer" class="toolbarHorizontalGroup">
              <div class="verticalToolbarSeparator" />

              <button
                id="currentOutlineItem"
                class="toolbarButton"
                type="button"
                :disabled="true"
                tabindex="0"
                data-l10n-id="pdfjs-current-outline-item-button"
              >
                <span data-l10n-id="pdfjs-current-outline-item-button-label" />
              </button>
            </div>
          </div>
        </div>
        <div id="sidebarContent">
          <div id="thumbnailView" />
          <div id="outlineView" class="hidden" />
          <div id="attachmentsView" class="hidden" />
          <div id="layersView" class="hidden" />
        </div>
        <div id="sidebarResizer" />
      </div>  <!-- sidebarContainer -->

      <div id="mainContainer">
        <div class="toolbar">
          <div id="toolbarContainer">
            <div id="toolbarViewer" class="toolbarHorizontalGroup">
              <div id="toolbarViewerLeft" class="toolbarHorizontalGroup">
                <button
                  id="sidebarToggleButton"
                  class="toolbarButton"
                  type="button"
                  tabindex="0"
                  data-l10n-id="pdfjs-toggle-sidebar-button"
                  aria-expanded="false"
                  aria-haspopup="true"
                  aria-controls="sidebarContainer"
                >
                  <span data-l10n-id="pdfjs-toggle-sidebar-button-label" />
                </button>
                <div class="toolbarButtonSpacer" />
                <div class="toolbarButtonWithContainer">
                  <button
                    id="viewFindButton"
                    class="toolbarButton"
                    type="button"
                    tabindex="0"
                    data-l10n-id="pdfjs-findbar-button"
                    aria-expanded="false"
                    aria-controls="findbar"
                  >
                    <span data-l10n-id="pdfjs-findbar-button-label" />
                  </button>
                  <div id="findbar" class="pdf-hidden doorHanger toolbarHorizontalGroup">
                    <div id="findInputContainer" class="toolbarHorizontalGroup">
                      <span class="loadingInput end toolbarHorizontalGroup">
                        <input
                          id="findInput"
                          class="toolbarField"
                          tabindex="0"
                          data-l10n-id="pdfjs-find-input"
                          aria-invalid="false"
                        />
                      </span>
                      <div class="toolbarHorizontalGroup">
                        <button
                          id="findPreviousButton"
                          class="toolbarButton"
                          type="button"
                          tabindex="0"
                          data-l10n-id="pdfjs-find-previous-button"
                        >
                          <span data-l10n-id="pdfjs-find-previous-button-label" />
                        </button>
                        <div class="splitToolbarButtonSeparator" />
                        <button
                          id="findNextButton"
                          class="toolbarButton"
                          type="button"
                          tabindex="0"
                          data-l10n-id="pdfjs-find-next-button"
                        >
                          <span data-l10n-id="pdfjs-find-next-button-label" />
                        </button>
                      </div>
                    </div>

                    <div id="findbarOptionsOneContainer" class="toolbarHorizontalGroup">
                      <div class="toggleButton toolbarLabel">
                        <input id="findHighlightAll" type="checkbox" tabindex="0" />
                        <label for="findHighlightAll" data-l10n-id="pdfjs-find-highlight-checkbox" />
                      </div>
                      <div class="toggleButton toolbarLabel">
                        <input id="findMatchCase" type="checkbox" tabindex="0" />
                        <label for="findMatchCase" data-l10n-id="pdfjs-find-match-case-checkbox-label" />
                      </div>
                    </div>
                    <div id="findbarOptionsTwoContainer" class="toolbarHorizontalGroup">
                      <div class="toggleButton toolbarLabel">
                        <input id="findMatchDiacritics" type="checkbox" tabindex="0" />
                        <label
                          for="findMatchDiacritics"
                          data-l10n-id="pdfjs-find-match-diacritics-checkbox-label"
                        />
                      </div>
                      <div class="toggleButton toolbarLabel">
                        <input id="findEntireWord" type="checkbox" tabindex="0" />
                        <label for="findEntireWord" data-l10n-id="pdfjs-find-entire-word-checkbox-label" />
                      </div>
                    </div>

                    <div id="findbarMessageContainer" class="toolbarHorizontalGroup" aria-live="polite">
                      <span id="findResultsCount" class="toolbarLabel" />
                      <span id="findMsg" class="toolbarLabel" />
                    </div>
                  </div>  <!-- findbar -->
                </div>
                <div class="toolbarHorizontalGroup hiddenSmallView">
                  <button
                    id="previous"
                    class="toolbarButton"
                    type="button"
                    tabindex="0"
                    data-l10n-id="pdfjs-previous-button"
                  >
                    <span data-l10n-id="pdfjs-previous-button-label" />
                  </button>
                  <div class="splitToolbarButtonSeparator" />
                  <button
                    id="next"
                    class="toolbarButton"
                    type="button"
                    tabindex="0"
                    data-l10n-id="pdfjs-next-button"
                  >
                    <span data-l10n-id="pdfjs-next-button-label" />
                  </button>
                </div>
                <div class="toolbarHorizontalGroup">
                  <span class="loadingInput start toolbarHorizontalGroup">
                    <input
                      id="pageNumber"
                      type="number"
                      class="toolbarField"
                      value="1"
                      min="1"
                      tabindex="0"
                      data-l10n-id="pdfjs-page-input"
                      autocomplete="off"
                    />
                  </span>
                  <span id="numPages" class="toolbarLabel" />
                </div>
              </div>
              <div id="toolbarViewerMiddle" class="toolbarHorizontalGroup">
                <div class="toolbarHorizontalGroup">
                  <button
                    id="zoomOutButton"
                    class="toolbarButton"
                    type="button"
                    tabindex="0"
                    data-l10n-id="pdfjs-zoom-out-button"
                  >
                    <span data-l10n-id="pdfjs-zoom-out-button-label" />
                  </button>
                  <div class="splitToolbarButtonSeparator" />
                  <button
                    id="zoomInButton"
                    class="toolbarButton"
                    type="button"
                    tabindex="0"
                    data-l10n-id="pdfjs-zoom-in-button"
                  >
                    <span data-l10n-id="pdfjs-zoom-in-button-label" />
                  </button>
                </div>
                <span id="scaleSelectContainer" class="dropdownToolbarButton">
                  <select id="scaleSelect" tabindex="0" data-l10n-id="pdfjs-zoom-select">
                    <option
                      id="pageAutoOption"
                      value="auto"
                      :selected="true"
                      data-l10n-id="pdfjs-page-scale-auto"
                    />
                    <option id="pageActualOption" value="page-actual" data-l10n-id="pdfjs-page-scale-actual" />
                    <option id="pageFitOption" value="page-fit" data-l10n-id="pdfjs-page-scale-fit" />
                    <option id="pageWidthOption" value="page-width" data-l10n-id="pdfjs-page-scale-width" />
                    <option
                      id="customScaleOption"
                      value="custom"
                      :disabled="true"
                      hidden="true"
                      data-l10n-id="pdfjs-page-scale-percent"
                      data-l10n-args="{ &quot;scale&quot;: 0 }"
                    />
                    <option
                      value="0.5"
                      data-l10n-id="pdfjs-page-scale-percent"
                      data-l10n-args="{ &quot;scale&quot;: 50 }"
                    />
                    <option
                      value="0.75"
                      data-l10n-id="pdfjs-page-scale-percent"
                      data-l10n-args="{ &quot;scale&quot;: 75 }"
                    />
                    <option
                      value="1"
                      data-l10n-id="pdfjs-page-scale-percent"
                      data-l10n-args="{ &quot;scale&quot;: 100 }"
                    />
                    <option
                      value="1.25"
                      data-l10n-id="pdfjs-page-scale-percent"
                      data-l10n-args="{ &quot;scale&quot;: 125 }"
                    />
                    <option
                      value="1.5"
                      data-l10n-id="pdfjs-page-scale-percent"
                      data-l10n-args="{ &quot;scale&quot;: 150 }"
                    />
                    <option
                      value="2"
                      data-l10n-id="pdfjs-page-scale-percent"
                      data-l10n-args="{ &quot;scale&quot;: 200 }"
                    />
                    <option
                      value="3"
                      data-l10n-id="pdfjs-page-scale-percent"
                      data-l10n-args="{ &quot;scale&quot;: 300 }"
                    />
                    <option
                      value="4"
                      data-l10n-id="pdfjs-page-scale-percent"
                      data-l10n-args="{ &quot;scale&quot;: 400 }"
                    />
                  </select>
                </span>
              </div>
              <div id="toolbarViewerRight" class="toolbarHorizontalGroup">
                <div id="editorModeButtons" class="toolbarHorizontalGroup" role="radiogroup">
                  <div id="editorSignature" class="toolbarButtonWithContainer" hidden="true">
                    <button
                      id="editorSignatureButton"
                      class="toolbarButton"
                      type="button"
                      tabindex="0"
                      :disabled="true"
                      role="radio"
                      aria-expanded="false"
                      aria-haspopup="true"
                      aria-controls="editorSignatureParamsToolbar"
                      data-l10n-id="pdfjs-editor-signature-button"
                    >
                      <span data-l10n-id="pdfjs-editor-signature-button-label" />
                    </button>
                    <div id="editorSignatureParamsToolbar" class="editorParamsToolbar hidden doorHangerRight menu">
                      <div
                        id="addSignatureDoorHanger"
                        class="menuContainer"
                        role="region"
                        data-l10n-id="pdfjs-editor-add-signature-container"
                      >
                        <button
                          id="editorSignatureAddSignature"
                          class="toolbarButton labeled"
                          type="button"
                          tabindex="0"
                          data-l10n-id="pdfjs-editor-signature-add-signature-button"
                        >
                          <span
                            data-l10n-id="pdfjs-editor-signature-add-signature-button-label"
                            class="editorParamsLabel"
                          />
                        </button>
                      </div>
                    </div>
                  </div>
                  <div id="editorHighlight" class="toolbarButtonWithContainer">
                    <button
                      id="editorHighlightButton"
                      class="toolbarButton"
                      type="button"
                      :disabled="true"
                      role="radio"
                      aria-expanded="false"
                      aria-haspopup="true"
                      aria-controls="editorHighlightParamsToolbar"
                      tabindex="0"
                      data-l10n-id="pdfjs-editor-highlight-button"
                    >
                      <span data-l10n-id="pdfjs-editor-highlight-button-label" />
                    </button>
                    <div id="editorHighlightParamsToolbar" class="editorParamsToolbar hidden doorHangerRight">
                      <div id="highlightParamsToolbarContainer" class="editorParamsToolbarContainer">
                        <div id="editorHighlightColorPicker" class="colorPicker">
                          <span
                            id="highlightColorPickerLabel"
                            class="editorParamsLabel"
                            data-l10n-id="pdfjs-editor-highlight-colorpicker-label"
                          />
                        </div>
                        <div id="editorHighlightThickness">
                          <label
                            for="editorFreeHighlightThickness"
                            class="editorParamsLabel"
                            data-l10n-id="pdfjs-editor-free-highlight-thickness-input"
                          />
                          <div class="thicknessPicker">
                            <input
                              id="editorFreeHighlightThickness"
                              type="range"
                              class="editorParamsSlider"
                              data-l10n-id="pdfjs-editor-free-highlight-thickness-title"
                              value="12"
                              min="8"
                              max="24"
                              step="1"
                              tabindex="0"
                            />
                          </div>
                        </div>
                        <div id="editorHighlightVisibility">
                          <div class="divider" />
                          <div class="toggler">
                            <label
                              for="editorHighlightShowAll"
                              class="editorParamsLabel"
                              data-l10n-id="pdfjs-editor-highlight-show-all-button-label"
                            />
                            <button
                              id="editorHighlightShowAll"
                              class="toggle-button"
                              type="button"
                              data-l10n-id="pdfjs-editor-highlight-show-all-button"
                              aria-pressed="true"
                              tabindex="0"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div id="editorFreeText" class="toolbarButtonWithContainer">
                    <button
                      id="editorFreeTextButton"
                      class="toolbarButton"
                      type="button"
                      :disabled="true"
                      role="radio"
                      aria-expanded="false"
                      aria-haspopup="true"
                      aria-controls="editorFreeTextParamsToolbar"
                      tabindex="0"
                      data-l10n-id="pdfjs-editor-free-text-button"
                    >
                      <span data-l10n-id="pdfjs-editor-free-text-button-label" />
                    </button>
                    <div id="editorFreeTextParamsToolbar" class="editorParamsToolbar hidden doorHangerRight">
                      <div class="editorParamsToolbarContainer">
                        <div class="editorParamsSetter">
                          <label
                            for="editorFreeTextColor"
                            class="editorParamsLabel"
                            data-l10n-id="pdfjs-editor-free-text-color-input"
                          />
                          <input
                            id="editorFreeTextColor"
                            type="color"
                            class="editorParamsColor"
                            tabindex="0"
                          />
                        </div>
                        <div class="editorParamsSetter">
                          <label
                            for="editorFreeTextFontSize"
                            class="editorParamsLabel"
                            data-l10n-id="pdfjs-editor-free-text-size-input"
                          />
                          <input
                            id="editorFreeTextFontSize"
                            type="range"
                            class="editorParamsSlider"
                            value="10"
                            min="5"
                            max="100"
                            step="1"
                            tabindex="0"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div id="editorInk" class="toolbarButtonWithContainer">
                    <button
                      id="editorInkButton"
                      class="toolbarButton"
                      type="button"
                      :disabled="true"
                      role="radio"
                      aria-expanded="false"
                      aria-haspopup="true"
                      aria-controls="editorInkParamsToolbar"
                      tabindex="0"
                      data-l10n-id="pdfjs-editor-ink-button"
                    >
                      <span data-l10n-id="pdfjs-editor-ink-button-label" />
                    </button>
                    <div id="editorInkParamsToolbar" class="editorParamsToolbar hidden doorHangerRight">
                      <div class="editorParamsToolbarContainer">
                        <div class="editorParamsSetter">
                          <label
                            for="editorInkColor"
                            class="editorParamsLabel"
                            data-l10n-id="pdfjs-editor-ink-color-input"
                          />
                          <input
                            id="editorInkColor"
                            type="color"
                            class="editorParamsColor"
                            tabindex="0"
                          />
                        </div>
                        <div class="editorParamsSetter">
                          <label
                            for="editorInkThickness"
                            class="editorParamsLabel"
                            data-l10n-id="pdfjs-editor-ink-thickness-input"
                          />
                          <input
                            id="editorInkThickness"
                            type="range"
                            class="editorParamsSlider"
                            value="1"
                            min="1"
                            max="20"
                            step="1"
                            tabindex="0"
                          />
                        </div>
                        <div class="editorParamsSetter">
                          <label
                            for="editorInkOpacity"
                            class="editorParamsLabel"
                            data-l10n-id="pdfjs-editor-ink-opacity-input"
                          />
                          <input
                            id="editorInkOpacity"
                            type="range"
                            class="editorParamsSlider"
                            value="1"
                            min="0.05"
                            max="1"
                            step="0.05"
                            tabindex="0"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div id="editorStamp" class="toolbarButtonWithContainer">
                    <button
                      id="editorStampButton"
                      class="toolbarButton"
                      type="button"
                      :disabled="true"
                      role="radio"
                      aria-expanded="false"
                      aria-haspopup="true"
                      aria-controls="editorStampParamsToolbar"
                      tabindex="0"
                      data-l10n-id="pdfjs-editor-stamp-button"
                    >
                      <span data-l10n-id="pdfjs-editor-stamp-button-label" />
                    </button>
                    <div id="editorStampParamsToolbar" class="editorParamsToolbar hidden doorHangerRight menu">
                      <div class="menuContainer">
                        <button
                          id="editorStampAddImage"
                          class="toolbarButton labeled"
                          type="button"
                          tabindex="0"
                          data-l10n-id="pdfjs-editor-stamp-add-image-button"
                        >
                          <span
                            class="editorParamsLabel"
                            data-l10n-id="pdfjs-editor-stamp-add-image-button-label"
                          />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <div id="editorModeSeparator" class="verticalToolbarSeparator" />

                <div class="toolbarHorizontalGroup hiddenMediumView hidden">
                  <button
                    id="printButton"
                    class="toolbarButton"
                    type="button"
                    tabindex="0"
                    data-l10n-id="pdfjs-print-button"
                  >
                    <span data-l10n-id="pdfjs-print-button-label" />
                  </button>

                  <button
                    id="downloadButton"
                    class="toolbarButton"
                    type="button"
                    tabindex="0"
                    data-l10n-id="pdfjs-save-button"
                  >
                    <span data-l10n-id="pdfjs-save-button-label" />
                  </button>
                </div>

                <div class="verticalToolbarSeparator hiddenMediumView hidden" />

                <div id="secondaryToolbarToggle" class="toolbarButtonWithContainer">
                  <button
                    id="secondaryToolbarToggleButton"
                    class="toolbarButton"
                    type="button"
                    tabindex="0"
                    data-l10n-id="pdfjs-tools-button"
                    aria-expanded="false"
                    aria-haspopup="true"
                    aria-controls="secondaryToolbar"
                  >
                    <span data-l10n-id="pdfjs-tools-button-label" />
                  </button>
                  <div id="secondaryToolbar" class="hidden doorHangerRight menu">
                    <div id="secondaryToolbarButtonContainer" class="menuContainer">
                      <button
                        id="secondaryOpenFile"
                        class="toolbarButton labeled"
                        type="button"
                        tabindex="0"
                        data-l10n-id="pdfjs-open-file-button"
                      >
                        <span data-l10n-id="pdfjs-open-file-button-label" />
                      </button>

                      <div class="visibleMediumView">
                        <button
                          id="secondaryPrint"
                          class="toolbarButton labeled"
                          type="button"
                          tabindex="0"
                          data-l10n-id="pdfjs-print-button"
                        >
                          <span data-l10n-id="pdfjs-print-button-label" />
                        </button>

                        <button
                          id="secondaryDownload"
                          class="toolbarButton labeled"
                          type="button"
                          tabindex="0"
                          data-l10n-id="pdfjs-save-button"
                        >
                          <span data-l10n-id="pdfjs-save-button-label" />
                        </button>
                      </div>

                      <div class="horizontalToolbarSeparator" />

                      <button
                        id="presentationMode"
                        class="toolbarButton labeled"
                        type="button"
                        tabindex="0"
                        data-l10n-id="pdfjs-presentation-mode-button"
                      >
                        <span data-l10n-id="pdfjs-presentation-mode-button-label" />
                      </button>

                      <a
                        id="viewBookmark"
                        href="#"
                        class="toolbarButton labeled"
                        tabindex="0"
                        data-l10n-id="pdfjs-bookmark-button"
                      >
                        <span data-l10n-id="pdfjs-bookmark-button-label" />
                      </a>

                      <div id="viewBookmarkSeparator" class="horizontalToolbarSeparator" />

                      <button
                        id="firstPage"
                        class="toolbarButton labeled"
                        type="button"
                        tabindex="0"
                        data-l10n-id="pdfjs-first-page-button"
                      >
                        <span data-l10n-id="pdfjs-first-page-button-label" />
                      </button>
                      <button
                        id="lastPage"
                        class="toolbarButton labeled"
                        type="button"
                        tabindex="0"
                        data-l10n-id="pdfjs-last-page-button"
                      >
                        <span data-l10n-id="pdfjs-last-page-button-label" />
                      </button>

                      <div class="horizontalToolbarSeparator" />

                      <button
                        id="pageRotateCw"
                        class="toolbarButton labeled"
                        type="button"
                        tabindex="0"
                        data-l10n-id="pdfjs-page-rotate-cw-button"
                      >
                        <span data-l10n-id="pdfjs-page-rotate-cw-button-label" />
                      </button>
                      <button
                        id="pageRotateCcw"
                        class="toolbarButton labeled"
                        type="button"
                        tabindex="0"
                        data-l10n-id="pdfjs-page-rotate-ccw-button"
                      >
                        <span data-l10n-id="pdfjs-page-rotate-ccw-button-label" />
                      </button>

                      <div class="horizontalToolbarSeparator" />

                      <div id="cursorToolButtons" role="radiogroup">
                        <button
                          id="cursorSelectTool"
                          class="toolbarButton labeled toggled"
                          type="button"
                          tabindex="0"
                          data-l10n-id="pdfjs-cursor-text-select-tool-button"
                          role="radio"
                          aria-checked="true"
                        >
                          <span data-l10n-id="pdfjs-cursor-text-select-tool-button-label" />
                        </button>
                        <button
                          id="cursorHandTool"
                          class="toolbarButton labeled"
                          type="button"
                          tabindex="0"
                          data-l10n-id="pdfjs-cursor-hand-tool-button"
                          role="radio"
                          aria-checked="false"
                        >
                          <span data-l10n-id="pdfjs-cursor-hand-tool-button-label" />
                        </button>
                      </div>

                      <div class="horizontalToolbarSeparator" />

                      <div id="scrollModeButtons" role="radiogroup">
                        <button
                          id="scrollPage"
                          class="toolbarButton labeled"
                          type="button"
                          tabindex="0"
                          data-l10n-id="pdfjs-scroll-page-button"
                          role="radio"
                          aria-checked="false"
                        >
                          <span data-l10n-id="pdfjs-scroll-page-button-label" />
                        </button>
                        <button
                          id="scrollVertical"
                          class="toolbarButton labeled toggled"
                          type="button"
                          tabindex="0"
                          data-l10n-id="pdfjs-scroll-vertical-button"
                          role="radio"
                          aria-checked="true"
                        >
                          <span data-l10n-id="pdfjs-scroll-vertical-button-label" />
                        </button>
                        <button
                          id="scrollHorizontal"
                          class="toolbarButton labeled"
                          type="button"
                          tabindex="0"
                          data-l10n-id="pdfjs-scroll-horizontal-button"
                          role="radio"
                          aria-checked="false"
                        >
                          <span data-l10n-id="pdfjs-scroll-horizontal-button-label" />
                        </button>
                        <button
                          id="scrollWrapped"
                          class="toolbarButton labeled"
                          type="button"
                          tabindex="0"
                          data-l10n-id="pdfjs-scroll-wrapped-button"
                          role="radio"
                          aria-checked="false"
                        >
                          <span data-l10n-id="pdfjs-scroll-wrapped-button-label" />
                        </button>
                      </div>

                      <div class="horizontalToolbarSeparator" />

                      <div id="spreadModeButtons" role="radiogroup">
                        <button
                          id="spreadNone"
                          class="toolbarButton labeled toggled"
                          type="button"
                          tabindex="0"
                          data-l10n-id="pdfjs-spread-none-button"
                          role="radio"
                          aria-checked="true"
                        >
                          <span data-l10n-id="pdfjs-spread-none-button-label" />
                        </button>
                        <button
                          id="spreadOdd"
                          class="toolbarButton labeled"
                          type="button"
                          tabindex="0"
                          data-l10n-id="pdfjs-spread-odd-button"
                          role="radio"
                          aria-checked="false"
                        >
                          <span data-l10n-id="pdfjs-spread-odd-button-label" />
                        </button>
                        <button
                          id="spreadEven"
                          class="toolbarButton labeled"
                          type="button"
                          tabindex="0"
                          data-l10n-id="pdfjs-spread-even-button"
                          role="radio"
                          aria-checked="false"
                        >
                          <span data-l10n-id="pdfjs-spread-even-button-label" />
                        </button>
                      </div>

                      <div id="imageAltTextSettingsSeparator" class="horizontalToolbarSeparator hidden" />
                      <button
                        id="imageAltTextSettings"
                        type="button"
                        class="toolbarButton labeled hidden"
                        tabindex="0"
                        data-l10n-id="pdfjs-image-alt-text-settings-button"
                        aria-controls="altTextSettingsDialog"
                      >
                        <span data-l10n-id="pdfjs-image-alt-text-settings-button-label" />
                      </button>

                      <div class="horizontalToolbarSeparator" />

                      <button
                        id="documentProperties"
                        class="toolbarButton labeled"
                        type="button"
                        tabindex="0"
                        data-l10n-id="pdfjs-document-properties-button"
                        aria-controls="documentPropertiesDialog"
                      >
                        <span data-l10n-id="pdfjs-document-properties-button-label" />
                      </button>
                    </div>
                  </div>  <!-- secondaryToolbar -->
                </div>
              </div>
            </div>
            <div id="loadingBar">
              <div class="progress">
                <div class="glimmer" />
              </div>
            </div>
          </div>
        </div>

        <div id="viewerContainer" tabindex="0">
          <div id="viewer" class="pdfViewer" />
        </div>
      </div> <!-- mainContainer -->

      <div id="dialogContainer">
        <dialog id="passwordDialog">
          <div class="row">
            <label id="passwordText" for="password" data-l10n-id="pdfjs-password-label" />
          </div>
          <div class="row">
            <input id="password" type="password" class="toolbarField" />
          </div>
          <div class="buttonRow">
            <button id="passwordCancel" class="dialogButton" type="button">
              <span
                data-l10n-id="pdfjs-password-cancel-button"
              />
            </button>
            <button id="passwordSubmit" class="dialogButton" type="button">
              <span
                data-l10n-id="pdfjs-password-ok-button"
              />
            </button>
          </div>
        </dialog>
        <dialog id="documentPropertiesDialog">
          <div class="row">
            <span id="fileNameLabel" data-l10n-id="pdfjs-document-properties-file-name" />
            <p id="fileNameField" aria-labelledby="fileNameLabel">
              -
            </p>
          </div>
          <div class="row">
            <span id="fileSizeLabel" data-l10n-id="pdfjs-document-properties-file-size" />
            <p id="fileSizeField" aria-labelledby="fileSizeLabel">
              -
            </p>
          </div>
          <div class="separator" />
          <div class="row">
            <span id="titleLabel" data-l10n-id="pdfjs-document-properties-title" />
            <p id="titleField" aria-labelledby="titleLabel">
              -
            </p>
          </div>
          <div class="row">
            <span id="authorLabel" data-l10n-id="pdfjs-document-properties-author" />
            <p id="authorField" aria-labelledby="authorLabel">
              -
            </p>
          </div>
          <div class="row">
            <span id="subjectLabel" data-l10n-id="pdfjs-document-properties-subject" />
            <p id="subjectField" aria-labelledby="subjectLabel">
              -
            </p>
          </div>
          <div class="row">
            <span id="keywordsLabel" data-l10n-id="pdfjs-document-properties-keywords" />
            <p id="keywordsField" aria-labelledby="keywordsLabel">
              -
            </p>
          </div>
          <div class="row">
            <span id="creationDateLabel" data-l10n-id="pdfjs-document-properties-creation-date" />
            <p id="creationDateField" aria-labelledby="creationDateLabel">
              -
            </p>
          </div>
          <div class="row">
            <span id="modificationDateLabel" data-l10n-id="pdfjs-document-properties-modification-date" />
            <p id="modificationDateField" aria-labelledby="modificationDateLabel">
              -
            </p>
          </div>
          <div class="row">
            <span id="creatorLabel" data-l10n-id="pdfjs-document-properties-creator" />
            <p id="creatorField" aria-labelledby="creatorLabel">
              -
            </p>
          </div>
          <div class="separator" />
          <div class="row">
            <span id="producerLabel" data-l10n-id="pdfjs-document-properties-producer" />
            <p id="producerField" aria-labelledby="producerLabel">
              -
            </p>
          </div>
          <div class="row">
            <span id="versionLabel" data-l10n-id="pdfjs-document-properties-version" />
            <p id="versionField" aria-labelledby="versionLabel">
              -
            </p>
          </div>
          <div class="row">
            <span id="pageCountLabel" data-l10n-id="pdfjs-document-properties-page-count" />
            <p id="pageCountField" aria-labelledby="pageCountLabel">
              -
            </p>
          </div>
          <div class="row">
            <span id="pageSizeLabel" data-l10n-id="pdfjs-document-properties-page-size" />
            <p id="pageSizeField" aria-labelledby="pageSizeLabel">
              -
            </p>
          </div>
          <div class="separator" />
          <div class="row">
            <span id="linearizedLabel" data-l10n-id="pdfjs-document-properties-linearized" />
            <p id="linearizedField" aria-labelledby="linearizedLabel">
              -
            </p>
          </div>
          <div class="buttonRow">
            <button id="documentPropertiesClose" class="dialogButton" type="button">
              <span
                data-l10n-id="pdfjs-document-properties-close-button"
              />
            </button>
          </div>
        </dialog>
        <dialog
          id="altTextDialog"
          class="dialog altText"
          aria-labelledby="dialogLabel"
          aria-describedby="dialogDescription"
        >
          <div id="altTextContainer" class="mainContainer">
            <div id="overallDescription">
              <span id="dialogLabel" data-l10n-id="pdfjs-editor-alt-text-dialog-label" class="title" />
              <span id="dialogDescription" data-l10n-id="pdfjs-editor-alt-text-dialog-description" />
            </div>
            <div id="addDescription">
              <div class="radio">
                <div class="radioButton">
                  <input
                    id="descriptionButton"
                    type="radio"
                    name="altTextOption"
                    tabindex="0"
                    aria-describedby="descriptionAreaLabel"
                    checked
                  />
                  <label for="descriptionButton" data-l10n-id="pdfjs-editor-alt-text-add-description-label" />
                </div>
                <div class="radioLabel">
                  <span
                    id="descriptionAreaLabel"
                    data-l10n-id="pdfjs-editor-alt-text-add-description-description"
                  />
                </div>
              </div>
              <div class="descriptionArea">
                <textarea
                  id="descriptionTextarea"
                  aria-labelledby="descriptionAreaLabel"
                  data-l10n-id="pdfjs-editor-alt-text-textarea"
                  tabindex="0"
                />
              </div>
            </div>
            <div id="markAsDecorative">
              <div class="radio">
                <div class="radioButton">
                  <input
                    id="decorativeButton"
                    type="radio"
                    name="altTextOption"
                    aria-describedby="decorativeLabel"
                  />
                  <label for="decorativeButton" data-l10n-id="pdfjs-editor-alt-text-mark-decorative-label" />
                </div>
                <div class="radioLabel">
                  <span id="decorativeLabel" data-l10n-id="pdfjs-editor-alt-text-mark-decorative-description" />
                </div>
              </div>
            </div>
            <div id="buttons">
              <button
                id="altTextCancel"
                class="secondaryButton"
                type="button"
                tabindex="0"
              >
                <span
                  data-l10n-id="pdfjs-editor-alt-text-cancel-button"
                />
              </button>
              <button
                id="altTextSave"
                class="primaryButton"
                type="button"
                tabindex="0"
              >
                <span
                  data-l10n-id="pdfjs-editor-alt-text-save-button"
                />
              </button>
            </div>
          </div>
        </dialog>
        <dialog
          id="newAltTextDialog"
          class="dialog newAltText"
          aria-labelledby="newAltTextTitle"
          aria-describedby="newAltTextDescription"
          tabindex="0"
        >
          <div id="newAltTextContainer" class="mainContainer">
            <div class="title">
              <span
                id="newAltTextTitle"
                data-l10n-id="pdfjs-editor-new-alt-text-dialog-edit-label"
                role="sectionhead"
                tabindex="0"
              />
            </div>
            <div id="mainContent">
              <div id="descriptionAndSettings">
                <div id="descriptionInstruction">
                  <div id="newAltTextDescriptionContainer">
                    <div class="altTextSpinner" role="status" aria-live="polite" />
                    <textarea
                      id="newAltTextDescriptionTextarea"
                      aria-labelledby="descriptionAreaLabel"
                      data-l10n-id="pdfjs-editor-new-alt-text-textarea"
                      tabindex="0"
                    />
                  </div>
                  <span
                    id="newAltTextDescription"
                    role="note"
                    data-l10n-id="pdfjs-editor-new-alt-text-description"
                  />
                  <div id="newAltTextDisclaimer" role="note">
                    <div>
                      <span data-l10n-id="pdfjs-editor-new-alt-text-disclaimer1" /> <a
                        id="newAltTextLearnMore"
                        href="https://support.mozilla.org/en-US/kb/pdf-alt-text"
                        target="_blank"
                        rel="noopener noreferrer"
                        data-l10n-id="pdfjs-editor-new-alt-text-disclaimer-learn-more-url"
                        tabindex="0"
                      />
                    </div>
                  </div>
                </div>
                <div id="newAltTextCreateAutomatically" class="toggler">
                  <button
                    id="newAltTextCreateAutomaticallyButton"
                    class="toggle-button"
                    type="button"
                    aria-pressed="true"
                    tabindex="0"
                  />
                  <label
                    for="newAltTextCreateAutomaticallyButton"
                    class="togglerLabel"
                    data-l10n-id="pdfjs-editor-new-alt-text-create-automatically-button-label"
                  />
                </div>
                <div id="newAltTextDownloadModel" class="hidden">
                  <span
                    id="newAltTextDownloadModelDescription"
                    data-l10n-id="pdfjs-editor-new-alt-text-ai-model-downloading-progress"
                    aria-valuemin="0"
                    data-l10n-args="{ &quot;totalSize&quot;: 0, &quot;downloadedSize&quot;: 0 }"
                  />
                </div>
              </div>
              <div id="newAltTextImagePreview" />
            </div>
            <div id="newAltTextError" class="messageBar">
              <div>
                <div>
                  <span class="title" data-l10n-id="pdfjs-editor-new-alt-text-error-title" />
                  <span class="description" data-l10n-id="pdfjs-editor-new-alt-text-error-description" />
                </div>
                <button
                  id="newAltTextCloseButton"
                  class="closeButton"
                  type="button"
                  tabindex="0"
                >
                  <span
                    data-l10n-id="pdfjs-editor-new-alt-text-error-close-button"
                  />
                </button>
              </div>
            </div>
            <div id="newAltTextButtons" class="dialogButtonsGroup">
              <button
                id="newAltTextCancel"
                type="button"
                class="secondaryButton hidden"
                tabindex="0"
              >
                <span
                  data-l10n-id="pdfjs-editor-alt-text-cancel-button"
                />
              </button>
              <button
                id="newAltTextNotNow"
                type="button"
                class="secondaryButton"
                tabindex="0"
              >
                <span
                  data-l10n-id="pdfjs-editor-new-alt-text-not-now-button"
                />
              </button>
              <button
                id="newAltTextSave"
                type="button"
                class="primaryButton"
                tabindex="0"
              >
                <span
                  data-l10n-id="pdfjs-editor-alt-text-save-button"
                />
              </button>
            </div>
          </div>
        </dialog>

        <dialog id="altTextSettingsDialog" class="dialog" aria-labelledby="altTextSettingsTitle">
          <div id="altTextSettingsContainer" class="mainContainer">
            <div class="title">
              <span
                id="altTextSettingsTitle"
                data-l10n-id="pdfjs-editor-alt-text-settings-dialog-label"
                role="sectionhead"
                tabindex="0"
                class="title"
              />
            </div>
            <div id="automaticAltText">
              <span data-l10n-id="pdfjs-editor-alt-text-settings-automatic-title" />
              <div id="automaticSettings">
                <div id="createModelSetting">
                  <div class="toggler">
                    <button
                      id="createModelButton"
                      type="button"
                      class="toggle-button"
                      aria-pressed="true"
                      tabindex="0"
                    />
                    <label
                      for="createModelButton"
                      class="togglerLabel"
                      data-l10n-id="pdfjs-editor-alt-text-settings-create-model-button-label"
                    />
                  </div>
                  <div id="createModelDescription" class="description">
                    <span data-l10n-id="pdfjs-editor-alt-text-settings-create-model-description" /> <a
                      id="altTextSettingsLearnMore"
                      href="https://support.mozilla.org/en-US/kb/pdf-alt-text"
                      target="_blank"
                      rel="noopener noreferrer"
                      data-l10n-id="pdfjs-editor-new-alt-text-disclaimer-learn-more-url"
                      tabindex="0"
                    />
                  </div>
                </div>
                <div id="aiModelSettings">
                  <div>
                    <span
                      data-l10n-id="pdfjs-editor-alt-text-settings-download-model-label"
                      data-l10n-args="{ &quot;totalSize&quot;: 180 }"
                    />
                    <div id="aiModelDescription" class="description">
                      <span data-l10n-id="pdfjs-editor-alt-text-settings-ai-model-description" />
                    </div>
                  </div>
                  <button
                    id="deleteModelButton"
                    type="button"
                    class="secondaryButton"
                    tabindex="0"
                  >
                    <span
                      data-l10n-id="pdfjs-editor-alt-text-settings-delete-model-button"
                    />
                  </button>
                  <button
                    id="downloadModelButton"
                    type="button"
                    class="secondaryButton"
                    tabindex="0"
                  >
                    <span
                      data-l10n-id="pdfjs-editor-alt-text-settings-download-model-button"
                    />
                  </button>
                </div>
              </div>
            </div>
            <div class="dialogSeparator" />
            <div id="altTextEditor">
              <span data-l10n-id="pdfjs-editor-alt-text-settings-editor-title" />
              <div id="showAltTextEditor">
                <div class="toggler">
                  <button
                    id="showAltTextDialogButton"
                    type="button"
                    class="toggle-button"
                    aria-pressed="true"
                    tabindex="0"
                  />
                  <label
                    for="showAltTextDialogButton"
                    class="togglerLabel"
                    data-l10n-id="pdfjs-editor-alt-text-settings-show-dialog-button-label"
                  />
                </div>
                <div id="showAltTextDialogDescription" class="description">
                  <span data-l10n-id="pdfjs-editor-alt-text-settings-show-dialog-description" />
                </div>
              </div>
            </div>
            <div id="buttons" class="dialogButtonsGroup">
              <button
                id="altTextSettingsCloseButton"
                type="button"
                class="primaryButton"
                tabindex="0"
              >
                <span
                  data-l10n-id="pdfjs-editor-alt-text-settings-close-button"
                />
              </button>
            </div>
          </div>
        </dialog>

        <dialog id="addSignatureDialog" class="dialog signatureDialog" aria-labelledby="addSignatureDialogLabel">
          <span id="addSignatureDialogLabel" data-l10n-id="pdfjs-editor-add-signature-dialog-label" />
          <div id="addSignatureContainer" class="mainContainer">
            <div class="title">
              <span role="sectionhead" data-l10n-id="pdfjs-editor-add-signature-dialog-title" tabindex="0" />
            </div>
            <div id="addSignatureOptions" role="tablist">
              <button
                id="addSignatureTypeButton"
                type="button"
                role="tab"
                aria-selected="true"
                aria-controls="addSignatureTypeContainer"
                data-l10n-id="pdfjs-editor-add-signature-type-button"
                tabindex="0"
              />
              <button
                id="addSignatureDrawButton"
                type="button"
                role="tab"
                aria-selected="false"
                aria-controls="addSignatureDrawContainer"
                data-l10n-id="pdfjs-editor-add-signature-draw-button"
                tabindex="0"
              />
              <button
                id="addSignatureImageButton"
                type="button"
                role="tab"
                aria-selected="false"
                aria-controls="addSignatureImageContainer"
                data-l10n-id="pdfjs-editor-add-signature-image-button"
                tabindex="-1"
              />
            </div>
            <div id="addSignatureActionContainer" data-selected="type">
              <div id="addSignatureTypeContainer" role="tabpanel" aria-labelledby="addSignatureTypeContainer">
                <input
                  id="addSignatureTypeInput"
                  type="text"
                  data-l10n-id="pdfjs-editor-add-signature-type-input"
                  tabindex="0"
                />
              </div>
              <div
                id="addSignatureDrawContainer"
                role="tabpanel"
                aria-labelledby="addSignatureDrawButton"
                tabindex="-1"
              >
                <svg
                  id="addSignatureDraw"
                  xmlns="http://www.w3.org/2000/svg"
                  aria-labelledby="addSignatureDrawPlaceholder"
                />
                <span
                  id="addSignatureDrawPlaceholder"
                  data-l10n-id="pdfjs-editor-add-signature-draw-placeholder"
                />
                <div id="thickness">
                  <div>
                    <label
                      for="addSignatureDrawThickness"
                      data-l10n-id="pdfjs-editor-add-signature-draw-thickness-range-label"
                    />
                    <input
                      id="addSignatureDrawThickness"
                      type="range"
                      min="1"
                      max="5"
                      step="1"
                      value="1"
                      data-l10n-id="pdfjs-editor-add-signature-draw-thickness-range"
                      data-l10n-args="{ &quot;thickness&quot;: 1 }"
                      tabindex="0"
                    />
                  </div>
                </div>
              </div>
              <div
                id="addSignatureImageContainer"
                role="tabpanel"
                aria-labelledby="addSignatureImageButton"
                tabindex="-1"
              >
                <svg
                  id="addSignatureImage"
                  xmlns="http://www.w3.org/2000/svg"
                  aria-labelledby="addSignatureImagePlaceholder"
                />
                <div id="addSignatureImagePlaceholder">
                  <span data-l10n-id="pdfjs-editor-add-signature-image-placeholder" />
                  <label id="addSignatureImageBrowse" for="addSignatureFilePicker" tabindex="0">
                    <a data-l10n-id="pdfjs-editor-add-signature-image-browse-link" />
                  </label>
                  <input id="addSignatureFilePicker" type="file" />
                </div>
              </div>
              <div id="addSignatureControls">
                <div id="horizontalContainer">
                  <div id="addSignatureDescriptionContainer">
                    <label
                      for="addSignatureDescInput"
                      data-l10n-id="pdfjs-editor-add-signature-description-label"
                    />
                    <span id="addSignatureDescription" class="inputWithClearButton">
                      <input
                        id="addSignatureDescInput"
                        type="text"
                        data-l10n-id="pdfjs-editor-add-signature-description-input"
                        tabindex="0"
                      />
                      <button
                        class="clearInputButton"
                        type="button"
                        tabindex="0"
                        aria-hidden="true"
                      />
                    </span>
                  </div>
                  <button
                    id="clearSignatureButton"
                    type="button"
                    data-l10n-id="pdfjs-editor-add-signature-clear-button"
                    tabindex="0"
                  >
                    <span data-l10n-id="pdfjs-editor-add-signature-clear-button-label" />
                  </button>
                </div>
                <div id="addSignatureSaveContainer">
                  <input id="addSignatureSaveCheckbox" type="checkbox" checked="true" />
                  <label for="addSignatureSaveCheckbox" data-l10n-id="pdfjs-editor-add-signature-save-checkbox" />
                  <span />
                  <span
                    id="addSignatureSaveWarning"
                    data-l10n-id="pdfjs-editor-add-signature-save-warning-message"
                  />
                </div>
              </div>
              <div id="addSignatureError" hidden="true" class="messageBar">
                <div>
                  <div>
                    <span class="title" data-l10n-id="pdfjs-editor-add-signature-image-upload-error-title" />
                    <span
                      class="description"
                      data-l10n-id="pdfjs-editor-add-signature-image-upload-error-description"
                    />
                  </div>
                  <button
                    id="addSignatureErrorCloseButton"
                    class="closeButton"
                    type="button"
                    tabindex="0"
                  >
                    <span
                      data-l10n-id="pdfjs-editor-add-signature-error-close-button"
                    />
                  </button>
                </div>
              </div>
              <div class="dialogButtonsGroup">
                <button
                  id="addSignatureCancelButton"
                  type="button"
                  class="secondaryButton"
                  tabindex="0"
                >
                  <span
                    data-l10n-id="pdfjs-editor-add-signature-cancel-button"
                  />
                </button>
                <button
                  id="addSignatureAddButton"
                  type="button"
                  class="primaryButton"
                  disabled
                  tabindex="0"
                >
                  <span
                    data-l10n-id="pdfjs-editor-add-signature-add-button"
                  />
                </button>
              </div>
            </div>
          </div>
        </dialog>

        <dialog
          id="editSignatureDescriptionDialog"
          class="dialog signatureDialog"
          aria-labelledby="editSignatureDescriptionTitle"
        >
          <div id="editSignatureDescriptionContainer" class="mainContainer">
            <div class="title">
              <span
                id="editSignatureDescriptionTitle"
                role="sectionhead"
                data-l10n-id="pdfjs-editor-edit-signature-dialog-title"
                tabindex="0"
              />
            </div>
            <div id="editSignatureDescriptionAndView">
              <div id="editSignatureDescriptionContainer">
                <label for="editSignatureDescInput" data-l10n-id="pdfjs-editor-add-signature-description-label" />
                <span id="editSignatureDescription" class="inputWithClearButton">
                  <input
                    id="editSignatureDescInput"
                    type="text"
                    data-l10n-id="pdfjs-editor-add-signature-description-input"
                    tabindex="0"
                  />
                  <button
                    class="clearInputButton"
                    type="button"
                    tabindex="0"
                    aria-hidden="true"
                  />
                </span>
              </div>
              <svg id="editSignatureView" xmlns="http://www.w3.org/2000/svg" />
            </div>
            <div class="dialogButtonsGroup">
              <button
                id="editSignatureCancelButton"
                type="button"
                class="secondaryButton"
                tabindex="0"
              >
                <span
                  data-l10n-id="pdfjs-editor-add-signature-cancel-button"
                />
              </button>
              <button
                id="editSignatureUpdateButton"
                type="button"
                class="primaryButton"
                disabled
                tabindex="0"
              >
                <span
                  data-l10n-id="pdfjs-editor-edit-signature-update-button"
                />
              </button>
            </div>
          </div>
        </dialog>

        <dialog id="printServiceDialog" style="min-width: 200px;">
          <div class="row">
            <span data-l10n-id="pdfjs-print-progress-message" />
          </div>
          <div class="row">
            <progress value="0" max="100" />
            <span
              data-l10n-id="pdfjs-print-progress-percent"
              data-l10n-args="{ &quot;progress&quot;: 0 }"
              class="relative-progress"
            >0%</span>
          </div>
          <div class="buttonRow">
            <button id="printCancel" class="dialogButton" type="button">
              <span
                data-l10n-id="pdfjs-print-progress-close-button"
              />
            </button>
          </div>
        </dialog>
      </div>  <!-- dialogContainer -->

      <div
        id="editorUndoBar"
        class="messageBar"
        role="status"
        aria-labelledby="editorUndoBarMessage"
        tabindex="-1"
        hidden
      >
        <div>
          <div>
            <span id="editorUndoBarMessage" class="description" />
          </div>
          <button
            id="editorUndoBarUndoButton"
            class="undoButton"
            type="button"
            tabindex="0"
            data-l10n-id="pdfjs-editor-undo-bar-undo-button"
          >
            <span data-l10n-id="pdfjs-editor-undo-bar-undo-button-label" />
          </button>
          <button
            id="editorUndoBarCloseButton"
            class="closeButton"
            type="button"
            tabindex="0"
            data-l10n-id="pdfjs-editor-undo-bar-close-button"
          >
            <span data-l10n-id="pdfjs-editor-undo-bar-close-button-label" />
          </button>
        </div>
      </div> <!-- editorUndoBar -->
    </div> <!-- outerContainer -->
    <div id="printContainer" />

    <!-- 预览模式底部操作栏 -->
    <div v-if="props.mode === 'reference'" class="h-10 absolute left-1/2 -translate-x-1/2 bottom-8 flex items-center justify-center bg-white p-2 rounded-[18px] dark:text-black" style="box-shadow: #0003 0 2px 12px 2px;">
      <div class="mx-2 cursor-pointer h-7 rounded-sm leading-7 hover:bg-gray-200" @click="handleToggleSidebar">
        <!-- <span class="text-sm">侧边栏</span> -->
        <UIcon v-if="openSidebar" name="i-fluent:panel-left-contract-28-filled" class="h-7 w-8 leading-7 text-gray-600" />
        <UIcon v-else name="i-fluent:panel-left-expand-28-filled" class="h-7 w-8 leading-7 text-gray-600" />
      </div>
      <div class="cursor-pointer h-7 leading-7 pl-1 pr-1 rounded-sm flex items-center justify-center hover:bg-gray-200" @click="handlePagePrev">
        <UIcon name="i-fluent-mdl2:caret-solid-left" class="text-gray-600" />
      </div>
      <div class="ml-2 mr-2">
        <span>
          <UInput
            id="pageNumber"
            v-model="currentPageNumber"
            type="number"
            min="1"
            size="md"
            :ui="{ base: 'py-1 px-1.5 text-sm bg-gray-100 dark:bg-gray-100 dark:text-gray-700 border-none outline-none focus:outline-none rounded-md shadow-none' }"
            @change="handlePageNumberChange"
          />
        </span>
        <span class="ml-2 mr-2">/</span>
        <span id="numPages" class="text-sm">{{ pageCount }}</span>
      </div>
      <div class="cursor-pointer h-7 leading-7 pl-1 pr-1 mr-2 rounded-sm flex items-center justify-center hover:bg-gray-200" @click="handleNextPage">
        <UIcon name="i-fluent-mdl2:caret-solid-right" class="text-gray-600" />
      </div>
    </div>

    <!-- 文件选择按钮 -->
    <input
      ref="fileInput"
      type="file"
      accept=".pdf,application/pdf"
      :hidden="true"
      :multiple="false"
      @change="handleFileSelected"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import './assets/web/viewer.css'
import './assets/extend/extend.css'

// 动态导入 pdf.mjs 以避免 SSR 时的 DOMMatrix 错误
if (import.meta.client) {
  import('./assets/build/pdf.mjs')
}

const props = defineProps({
  mode: {
    type: String as PropType<'default' | 'reference'>,
    default: 'default'
  }
})
const emit = defineEmits(['loaded', 'loading-failed', 'page-loaded', 'ready'])

const pdfViewer = ref(null)

onMounted(async () => {
  await nextTick()

  // 等待容器完全渲染并检查可见性
  const initWithRetry = async (retryCount = 0) => {
    const container = document.getElementById('outerContainer')
    if (!container) {
      if (retryCount < 10) {
        setTimeout(() => initWithRetry(retryCount + 1), 100)
      }
      else {
        console.error('PDF容器未找到，初始化失败')
      }
      return
    }

    // 检查容器是否可见且有尺寸
    const rect = container.getBoundingClientRect()
    const computedStyle = getComputedStyle(container)
    const isVisible = rect.width > 0
      && rect.height > 0
      && container.offsetParent !== null
      && computedStyle.display !== 'none'
      && computedStyle.visibility !== 'hidden'
      && computedStyle.opacity !== '0'

    console.log(`PDF容器检查 (尝试 ${retryCount + 1}):`, {
      width: rect.width,
      height: rect.height,
      offsetParent: !!container.offsetParent,
      display: computedStyle.display,
      visibility: computedStyle.visibility,
      opacity: computedStyle.opacity,
      isVisible
    })

    if (!isVisible) {
      if (retryCount < 20) {
        setTimeout(() => initWithRetry(retryCount + 1), 100)
      }
      else {
        console.error('PDF容器不可见，初始化失败')
      }
      return
    }

    try {
      await nextTick()
      await initPdfViewer()
    }
    catch (error) {
      console.error('PDF查看器初始化失败:', error)
    }
  }

  setTimeout(() => initWithRetry(), 100)
})

const initPdfViewer = async () => {
  try {
    console.log('开始初始化 PDF.js...')

    // 首先检查必要的 DOM 元素是否存在
    const requiredElements = ['outerContainer', 'viewerContainer', 'viewer']
    for (const elementId of requiredElements) {
      const element = document.getElementById(elementId)
      if (!element) {
        throw new Error(`必需的 DOM 元素 ${elementId} 不存在`)
      }
      console.log(`✓ DOM 元素 ${elementId} 已找到`)
    }

    // 首先导入 PDF.js 核心库并配置 worker
    console.log('正在导入 PDF.js 核心库...')
    const pdfjs = await import('./assets/build/pdf.mjs')
    console.log('✓ PDF.js 核心库导入成功，版本:', pdfjs.version)

    // 设置 worker 路径 - 使用绝对路径确保正确加载
    const workerPath = '/app/components/FreePdf/assets/build/pdf.worker.mjs'
    pdfjs.GlobalWorkerOptions.workerSrc = workerPath
    console.log('✓ PDF.js worker 路径设置为:', workerPath)

    // 测试 worker 是否可以正确加载
    try {
      const response = await fetch(workerPath, { method: 'HEAD' })
      if (!response.ok) {
        throw new Error(`Worker 文件无法访问: ${response.status}`)
      }
      console.log('✓ Worker 文件可访问')
    } catch (workerError) {
      console.warn('Worker 文件检查失败:', workerError)
    }

    // 动态导入查看器脚本，确保 DOM 已加载
    console.log('正在导入 PDF.js 查看器...')
    const viewer = await import('./assets/web/viewer.mjs')
    pdfViewer.value = viewer
    console.log('✓ PDF.js 查看器导入成功')

    // 设置模式
    if (!viewer.PDFViewerApplication.customConfig) {
      viewer.PDFViewerApplication.customConfig = {}
    }
    viewer.PDFViewerApplication.customConfig.mode = props.mode
    console.log('✓ PDF.js 模式设置为:', props.mode)

    console.log('✅ PDF.js 初始化完成')
  } catch (error) {
    console.error('❌ PDF.js 初始化失败:', error)
    throw error
  }

  // 监听文档加载完成事件
  viewer.PDFViewerApplication.eventBus._on('documentloaded', (event) => {
    console.log('PDF 文档加载完成:', event.source)
    nextTick(() => {
      // 等待 PDF 页面完全渲染后再进行页面跳转
      const waitForPagesReady = () => {
        const viewerContainer = document.getElementById('viewerContainer')
        const firstPage = viewerContainer?.querySelector('.page')

        if (firstPage && firstPage.offsetParent !== null) {
          console.log('PDF 页面已准备就绪')
          // 页面已经准备好，可以安全地进行滚动操作
          setTimeout(() => {
            const pageNumberInput = document.getElementById('pageNumber') as HTMLInputElement | null
            if (pageNumberInput) {
              pageNumberInput.value = '1'
              pageNumberInput.dispatchEvent(new Event('change'))
            }
          }, 500)
        } else {
          console.log('PDF 页面还未准备就绪，继续等待...')
          // 页面还没准备好，继续等待
          setTimeout(waitForPagesReady, 100)
        }
      }

      // 延迟执行，确保 DOM 完全渲染
      setTimeout(waitForPagesReady, 200)

      // 强制触发响应式更新
      handlePageValueChange()

      emit('loaded', event.source)
    })
  })
  viewer.PDFViewerApplication.eventBus._on('documenterror', (event) => {
    console.error('PDF 文档加载错误:', event)
    emit('loading-failed', event.source)
  })

  // 监听文档初始化事件
  viewer.PDFViewerApplication.eventBus._on('documentinit', (event) => {
    console.log('PDF 文档初始化:', event.source)
  })

  // 监听页面渲染事件
  viewer.PDFViewerApplication.eventBus._on('pagerendered', (event) => {
    console.log(`PDF 页面 ${event.pageNumber} 渲染完成`)
    emit('page-loaded', event.source)
    // 当页面渲染完成时触发响应式更新
    nextTick(() => {
      // 强制触发响应式更新
      handlePageValueChange()
    })
  })

  // 监听页面渲染开始事件
  viewer.PDFViewerApplication.eventBus._on('pagerender', (event) => {
    console.log(`PDF 页面 ${event.pageNumber} 开始渲染`)
  })

  // 监听页面变化事件
  viewer.PDFViewerApplication.eventBus._on('pagechanging', (event) => {
    // 当页面变化时触发响应式更新
    nextTick(() => {
      // 强制触发响应式更新
      handlePageValueChange()
    })
  })

  // 监听侧边栏变化事件
  viewer.PDFViewerApplication.eventBus._on('sidebarviewchanged', (event) => {
    // 当侧边栏变化时触发响应式更新
    nextTick(() => {
      // 强制触发响应式更新
      handlePageValueChange()
    })
  })

  emit('ready', viewer)
}

// 打开文件
const fileInput = ref(null)
const openFile = (pdfFile: File) => {
  try {
    if (!pdfViewer.value) {
      console.error('PDF查看器尚未初始化')
      return
    }

    if (pdfFile) {
      // 构造与原 viewer 期望一致的对象，包含 files 属性
      const fileInputLike = {
        files: [pdfFile]
      }
      pdfViewer.value.PDFViewerApplication.eventBus.dispatch('fileinputchange', {
        source: pdfViewer.value,
        fileInput: fileInputLike
      })
    }
    else {
      // 检查文件输入元素是否存在
      if (!fileInput.value) {
        console.error('文件输入元素不存在，可能组件尚未完全挂载')
        return
      }

      // 触发文件选择对话框
      fileInput.value.click()
    }
  }
  catch (error) {
    console.error('打开文件失败:', error)
  }
}
const handleFileSelected = async (_event) => {
  const { files } = _event.target
  if (!files || files.length === 0) {
    return
  }

  // 获取选择的文件
  const selectedFile = files[0]

  // 将文件设置到FileStore中
  const { useFileStore } = await import('@/store/FileStore')
  const fileStore = useFileStore()
  fileStore.setFile(selectedFile)

  pdfViewer.value.PDFViewerApplication.eventBus.dispatch('fileinputchange', {
    source: pdfViewer.value,
    fileInput: _event.target
  })
}

// 当前页数
const currentPageNumber = ref(1)
const pageCount = ref(0)
const openSidebar = ref(false)
const handlePageValueChange = (_event?) => {
  currentPageNumber.value = pdfViewer.value?.PDFViewerApplication?.page

  if (pdfViewer.value && pdfViewer.value.PDFViewerApplication) {
    currentPageNumber.value = pdfViewer.value.PDFViewerApplication.page
    pageCount.value = pdfViewer.value.PDFViewerApplication.pagesCount
    openSidebar.value = pdfViewer.value.PDFViewerApplication.pdfSidebar.isOpen
  }
  else {
    currentPageNumber.value = 1
    pageCount.value = 0
    openSidebar.value = false
  }
}

/**
 * 参考模式下，页码输入框变化事件
 * @param event 输入框事件
 */
const handlePageNumberChange = (event: InputEvent) => {
  const newPageNumber = (event.target as HTMLInputElement).value
  const pageNumberInput = document.getElementById('pageNumber') as HTMLInputElement | null
  if (pageNumberInput) {
    pageNumberInput.value = newPageNumber
    pageNumberInput.dispatchEvent(new Event('change'))
  }
}

/**
 * 上一页
 */
const handlePagePrev = () => {
  const pageNumberInput = document.getElementById('pageNumber') as HTMLInputElement | null
  const currentPageNumber = parseInt(pageNumberInput.value)
  if (pageNumberInput && currentPageNumber > 1) {
    pageNumberInput.value = (currentPageNumber - 1).toString()
    pageNumberInput.dispatchEvent(new Event('change'))
  }
}
/**
 * 下一页
 */
const handleNextPage = () => {
  const pageNumberInput = document.getElementById('pageNumber') as HTMLInputElement | null
  const currentPageNumber = parseInt(pageNumberInput.value)
  if (pageNumberInput && currentPageNumber < pageCount.value) {
    pageNumberInput.value = (currentPageNumber + 1).toString()
    pageNumberInput.dispatchEvent(new Event('change'))
  }
}

/**
 * 参考模式下，目录按钮点击事件,切换侧边栏显示状态
 * @param _event
 */
const handleToggleSidebar = (_event) => {
  // 触发点击事件
  const sidebarToggleButton = window.document.querySelector('#sidebarToggleButton') as HTMLElement
  if (sidebarToggleButton) {
    sidebarToggleButton.click()
  }
}

defineExpose({ openFile })
</script>

<style scoped>
.toolbarButton {
  cursor: pointer;
}
.toolbarLabel,
.toolbarLabel * {
  cursor: pointer;
}

.reference-mode .toolbar {
  visibility: hidden;
  position: absolute;
  top: -1000px;
}

.reference-mode #sidebarContainer {
  top: 0;
}

.reference-mode #viewerContainer {
  top: 0;
}

/* 调整缩略图样式 */
:deep(.thumbnailImage) {
  height: 100%;
}

/* 滚动条容器 */
/* 隐藏默认滚动条 */
.reference-mode #viewerContainer::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* 滚动条轨道 - 始终透明 */
.reference-mode #viewerContainer::-webkit-scrollbar-track {
  background: transparent;
}

/* 滚动条滑块 - 默认透明（隐藏） */
.reference-mode #viewerContainer::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  min-height: 80px;
  min-width: 80px;
}

/* 鼠标移入容器时显示滚动条滑块 */
.reference-mode #viewerContainer:hover::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5); /* 半透明灰色 */
}

/* 滑块 hover 时加深颜色 */
.reference-mode #viewerContainer::-webkit-scrollbar-thumb:hover {
  background-color: rgba(107, 114, 128, 0.7); /* 深色半透明 */
}

/* 边角样式 */
.reference-mode #viewerContainer::-webkit-scrollbar-corner {
  background: transparent;
}
</style>

<style>
.dark #sidebarContent #thumbnailView .thumbnail.selected {
  border-color: rgb(59, 130, 246) !important; /* 黑暗模式下设置白色边框 */
}
</style>
